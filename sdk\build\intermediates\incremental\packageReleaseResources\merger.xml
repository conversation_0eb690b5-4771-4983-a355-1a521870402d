<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\rs\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\emas-services\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\rs\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\emas-services\release"><file path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\generated\res\emas-services\release\ams_values\ams_values.xml" qualifiers=""><string name="ams_accountId">175421</string><string name="ams_appKey">*********</string><string name="ams_appSecret">e88e1aa048214275992f8d01d9ea5ffd</string><string name="ams_hotfix_idSecret">*********-1</string><string name="ams_hotfix_rsaSecret">MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCWnJFUILAxpANzdNxqyjdg7Q9IrVJIFyQrQvfs5w5yoW3tPanepUEtfuqJc10MLAkbEYrooogcu88Rvi+DXDNMfiiTG3jV9eApSnZs3ezdLpS08BwnpUpBBovAn55Di3Ei4lPLuTAxW7+TK0SyjnqyKhsqN2l8M/wfOwJ32twyiOYPjeZa1Y8yxMykLRSCStOiPqXDG+hGfNcegVI5/eU10VF6JQny31IZETcOc3iOc7nxrlT3dLy9J9fkrXfiSGgcT354Jxe2r54Zk1DwOQZHNW+0MAUtZCaU27uJH6520DofmUv50E4InHMMb9Jyopr1pJiVeltXOS1Py8zAUMMBAgMBAAECggEAPnEtI4bl5b7NG3oQxEmQSiQemRAas/68JR9/sH1fFRFuhNRy7/btndInpNLqedhr8ggE4kw4SElpIT8Lbde1APqxEcRrBbIJvLS9godD47OjPZzq31j8/xaarELBF9nhDwhsm2Ls4xnZxEdFFQ5TOjiQyKDun+rhlYUekdhwQJa0Cpv0etTZVv5TCVAH0vC2yykcOOoibQ/ZhE/LcMvr0BzpTARnutmTgwvpu+cjP3/aT3Yy3juj/CLrNeaiTr2FYnPgKcXzuzD7cEgcZjVdiAQMqb4DFzlnktORWsqWLy0DEzctQirSkfyxgnNTFzpEBQgTZNqV5WCECw89FfSiEQKBgQDUzonbdP+sNlq2ZMbyZt9Dr+0kDSFgBgt8soNSRAbLGqsg1tx68bv2AVER95dlxzXGwLY+AJMaMwkDCpyKvs+d9gRzFMNspOqPSxWyI/+l8Ks/lqegx+02MpYKynnFrUfP+ydo9fCs2Lqymh5mS7WRVnOVSEx2B140hLHO29UL/QKBgQC1LlxS95yBmB9Vf+s73dpdg0YaSZPNk1e619c41a7GQGo/0Sk5W5gS3rnL8R36POaOF6EJ2sV2LwprqLTrnj1YpTFruFqn/x4FpWgMIBvU/GLuJ71ajsi0EDKP+1DPz/AXAI4I4M4IPDP9c5A0TcTJCFQa6ceFTdXQbMbyUhloVQKBgQCoKX3umYngQCN2tjQwIPKUvlSahHW3N4+kPjxfqbnkjXJlROR9ksCBKZEyrYBJwR3RrT9Vx4Z20ZVxfdUuHpJZtKrXdbToCXoBOcgvORsvufrZCOf1uYbNqTO/2zhW6RMyWrlNy+sf6zO9JuSW2YTlEHfNo5AoRLJAtgr79+Nm0QKBgCsYxQNDPOoiqklnLShHZFrcD//Owqaixrp9/wbY5ULpLZzSfvDS1cCfVCiryQquE9V2z2qNrGypUdw7/BUTxdnhKXlLF91gNN748l/3/0bRZiLqNwNkdV9Hfw96o0VbW+vMyPxZpQiWcXN3WrTmTsnW/lKMi1YtJo/2X/hiCQMlAoGAF0BCBGLUtQxOOMw5VR/fAIc+XDeM91v6TBBKetjcVFr3YpyeX/Dfe+Sc2PRXcsIsy7IfZnv+QvyfKpWNyxjbWNdeI8Gf6cSriAHlwta6IH+q5KR9Bgoikl86Su+HTKd/QFWnvBgopVLDIvxHMh/oAgGVOjaq90EDMnpB0IbSbNI=</string><string name="ams_httpdns_secretKey">d19c391296790ff03403232ebffd6ab2</string><string name="ams_packageName">com.edog.car</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\socket\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\api\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\socket\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\api\unspecified\release\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\res"><file path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\res\values\values.xml" qualifiers=""><color name="colorhahaha">#9C27B0</color></file></source></dataSet><mergedItems/></merger>