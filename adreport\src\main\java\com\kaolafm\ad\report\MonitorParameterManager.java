package com.kaolafm.ad.report;

import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;

import com.kaolafm.ad.report.parameter.MiaoZhenParameter;
import com.kaolafm.ad.report.parameter.ParameterOptions;
import com.kaolafm.ad.report.parameter.TalkingDataParameter;
import com.kaolafm.ad.report.parameter.TalkingDataParameterOptions;
import com.kaolafm.ad.report.util.ADReportParameterUtil;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.base.utils.MD5;
import com.kaolafm.base.utils.StringUtil;

import java.util.Map;

public class MonitorParameterManager {

    private final MiaoZhenParameter miaoZhenParameter = new MiaoZhenParameter();
    private final TalkingDataParameter talkingDataParameter = new TalkingDataParameter();

    private MonitorParameterManager(){
    }

    public static class KRADIO_MONITOR_PARAMETER_MANAGER{
        private static final MonitorParameterManager INSTANCE = new MonitorParameterManager();
    }

    public static MonitorParameterManager getInstance(){
        return MonitorParameterManager.KRADIO_MONITOR_PARAMETER_MANAGER.INSTANCE;
    }

    public void loadMonitorParameter(){
        loadMiaozhenParameter();
        loadTalkingDataParameter();
    }

    public void loadMonitorParameterOptions(ParameterOptions parameterOptions,int eventType){
        switch (eventType) {
            case AdReportAgent.EventType.TALKING_DATA_MONITOR:
                setTalkingDataParameterOptions((TalkingDataParameterOptions) parameterOptions);
                break;
            case AdReportAgent.EventType.MIAOZHEN_MONITOR:
                break;
        }
    }

    public String getMonitorParameter(String url,int eventType){
        switch (eventType) {
            case AdReportAgent.EventType.TALKING_DATA_MONITOR:
                return macroTalkingDataParameter(url);
            case AdReportAgent.EventType.MIAOZHEN_MONITOR:
                return macroMiaozhenParameter(url);
        }
        return url;
    }

    public String macroMiaozhenParameter(String url){
        //使用map替换三方监测URL中需要替换的value
        Map map = ADReportParameterUtil.getUrlParams(url);
        map.put("mo",miaoZhenParameter.getMo());
        map.put("ns",miaoZhenParameter.getNs());
        map.put("m2",miaoZhenParameter.getM2());
        map.put("m1a",miaoZhenParameter.getM1a());
        map.put("m1",miaoZhenParameter.getM1());
        map.put("m6",miaoZhenParameter.getM6());
        map.put("m6a",miaoZhenParameter.getM6a());
        map.put("nn",miaoZhenParameter.getNn());
        //替换完毕返回url
        return ADReportParameterUtil.getUrlParamsByMap(map,url);
    }

    public String macroTalkingDataParameter(String url){
        //使用map替换三方监测URL中需要替换的value
        Map<String, Object> map = ADReportParameterUtil.getUrlParams(url);
        putMap(map,"ip",talkingDataParameter.getIp());
        putMap(map,"mac",talkingDataParameter.getMac());
        putMap(map,"androidid",talkingDataParameter.getAndroidid());
        putMap(map,"imei",talkingDataParameter.getImei());
        putMap(map,"osversion",talkingDataParameter.getOsversion());
        putMap(map,"vin",talkingDataParameter.getVin());
        putMap(map,"motocity",talkingDataParameter.getMotocity());
        putMap(map,"location",talkingDataParameter.getLocation());
        putMap(map,"mototype",talkingDataParameter.getMototype());
        putMap(map,"motostate",talkingDataParameter.getMotostate());
        putMap(map,"screentype",talkingDataParameter.getScreentype());

        //替换完毕返回url
        return ADReportParameterUtil.getUrlParamsByMap(map,url);
    }

    private void putMap(Map map,String key,Object value){
        if (value == null || "".equals(value)) {
            return;
        }
        map.put(key,value);
    }


    private void loadMiaozhenParameter(){
        Context context = AdReportManager.getInstance().getContext();
        miaoZhenParameter.setMo("1");
        String ip = ADReportParameterUtil.getIpAddress(context);
        if (!TextUtils.isEmpty(ip)) {
            miaoZhenParameter.setNs(ip);
        }
        miaoZhenParameter.setM2(DeviceUtil.getImei(context));
        String androidId = Settings.System.getString(context.getContentResolver(), Settings.System.ANDROID_ID);
        miaoZhenParameter.setM1a(MD5.getMD5Str(androidId));
        miaoZhenParameter.setM1(androidId);
        String mac = ADReportParameterUtil.getMacAddress(context);
        miaoZhenParameter.setM6(mac);
        if (!TextUtils.isEmpty(mac)) {
            miaoZhenParameter.setM6a(mac.split(":").toString());
        }
        String appName = ADReportParameterUtil.getAppName(context);
        miaoZhenParameter.setNn(ADReportParameterUtil.encode(appName));
    }

    //http://doc.talkingdata.com/posts/465 参考文档
    private void loadTalkingDataParameter(){
        Context context = AdReportManager.getInstance().getContext();
        talkingDataParameter.setDevicetype(ADReportParameterUtil.getModel());
        String ip = ADReportParameterUtil.getIpAddress(context);
        if (!TextUtils.isEmpty(ip)) {
            talkingDataParameter.setIp(ip);
        }
        String mac = ADReportParameterUtil.getMacAddress(context);
        if (!TextUtils.isEmpty(mac)) {
            talkingDataParameter.setMac(mac);
            talkingDataParameter.setMac_md5(MD5.getMD5Str(mac.split(":").toString().toUpperCase()).toUpperCase());
        }
        String androidId = Settings.System.getString(context.getContentResolver(), Settings.System.ANDROID_ID);
        if (!TextUtils.isEmpty(androidId)) {
            talkingDataParameter.setAndroidid(androidId);
            talkingDataParameter.setAndroidid_md5(MD5.getMD5Str(androidId.toUpperCase()).toUpperCase());
        }

        String imei = DeviceUtil.getImei(context);
        if (!TextUtils.isEmpty(imei)) {
            talkingDataParameter.setImei(imei);
            String hexStr = StringUtil.str2HexStr(MD5.getMD5Str(imei.toLowerCase()));
            talkingDataParameter.setImei_md5(hexStr);
        }
        talkingDataParameter.setPname(context.getPackageName());
        talkingDataParameter.setOsversion("0");
    }

    private void setTalkingDataParameterOptions(TalkingDataParameterOptions talkingDataParameterOptions){
        if (talkingDataParameterOptions != null) {
            talkingDataParameter.setVin(talkingDataParameterOptions.getVin());
            talkingDataParameter.setMotocity(talkingDataParameterOptions.getMotocity());
            talkingDataParameter.setLocation(talkingDataParameterOptions.getLocation());
            talkingDataParameter.setMototype(talkingDataParameterOptions.getMototype());
            talkingDataParameter.setMotostate(talkingDataParameterOptions.getMotostate());
            talkingDataParameter.setScreentype(talkingDataParameterOptions.getScreentype());
        }
    }

    public void setLocation(String lng, String lat) {
        talkingDataParameter.setLocation(lat + "," + lng);
    }

}
