1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kaolafm.opensdk"
4    android:versionCode="309"
5    android:versionName="3.0.8.exo_alpha28_SGM557.pre2" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml:4:5-81
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml:4:22-78
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\sdk\src\main\AndroidManifest.xml:5:22-76
13
14</manifest>
