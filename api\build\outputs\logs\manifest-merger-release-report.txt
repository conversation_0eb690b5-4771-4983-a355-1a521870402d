-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
	package
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:11-44
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml:1:1-46
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\api\src\main\AndroidManifest.xml
