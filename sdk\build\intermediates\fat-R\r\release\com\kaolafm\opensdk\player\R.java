package com.kaolafm.opensdk.player;

public final class R {
  public static final class anim {
    public static final int abc_fade_in = com.kaolafm.opensdk.R.anim.abc_fade_in;
    public static final int abc_fade_out = com.kaolafm.opensdk.R.anim.abc_fade_out;
    public static final int abc_grow_fade_in_from_bottom = com.kaolafm.opensdk.R.anim.abc_grow_fade_in_from_bottom;
    public static final int abc_popup_enter = com.kaolafm.opensdk.R.anim.abc_popup_enter;
    public static final int abc_popup_exit = com.kaolafm.opensdk.R.anim.abc_popup_exit;
    public static final int abc_shrink_fade_out_from_bottom = com.kaolafm.opensdk.R.anim.abc_shrink_fade_out_from_bottom;
    public static final int abc_slide_in_bottom = com.kaolafm.opensdk.R.anim.abc_slide_in_bottom;
    public static final int abc_slide_in_top = com.kaolafm.opensdk.R.anim.abc_slide_in_top;
    public static final int abc_slide_out_bottom = com.kaolafm.opensdk.R.anim.abc_slide_out_bottom;
    public static final int abc_slide_out_top = com.kaolafm.opensdk.R.anim.abc_slide_out_top;
    public static final int abc_tooltip_enter = com.kaolafm.opensdk.R.anim.abc_tooltip_enter;
    public static final int abc_tooltip_exit = com.kaolafm.opensdk.R.anim.abc_tooltip_exit;
    public static final int btn_checkbox_to_checked_box_inner_merged_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_checked_box_inner_merged_animation;
    public static final int btn_checkbox_to_checked_box_outer_merged_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_checked_box_outer_merged_animation;
    public static final int btn_checkbox_to_checked_icon_null_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_checked_icon_null_animation;
    public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_unchecked_box_inner_merged_animation;
    public static final int btn_checkbox_to_unchecked_check_path_merged_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_unchecked_check_path_merged_animation;
    public static final int btn_checkbox_to_unchecked_icon_null_animation = com.kaolafm.opensdk.R.anim.btn_checkbox_to_unchecked_icon_null_animation;
    public static final int btn_radio_to_off_mtrl_dot_group_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_off_mtrl_dot_group_animation;
    public static final int btn_radio_to_off_mtrl_ring_outer_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_off_mtrl_ring_outer_animation;
    public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_off_mtrl_ring_outer_path_animation;
    public static final int btn_radio_to_on_mtrl_dot_group_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_on_mtrl_dot_group_animation;
    public static final int btn_radio_to_on_mtrl_ring_outer_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_on_mtrl_ring_outer_animation;
    public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = com.kaolafm.opensdk.R.anim.btn_radio_to_on_mtrl_ring_outer_path_animation;
    public static final int fragment_close_enter = com.kaolafm.opensdk.R.anim.fragment_close_enter;
    public static final int fragment_close_exit = com.kaolafm.opensdk.R.anim.fragment_close_exit;
    public static final int fragment_fade_enter = com.kaolafm.opensdk.R.anim.fragment_fade_enter;
    public static final int fragment_fade_exit = com.kaolafm.opensdk.R.anim.fragment_fade_exit;
    public static final int fragment_fast_out_extra_slow_in = com.kaolafm.opensdk.R.anim.fragment_fast_out_extra_slow_in;
    public static final int fragment_open_enter = com.kaolafm.opensdk.R.anim.fragment_open_enter;
    public static final int fragment_open_exit = com.kaolafm.opensdk.R.anim.fragment_open_exit;
    }
  public static final class attr {
    public static final int actionBarDivider = com.kaolafm.opensdk.R.attr.actionBarDivider;
    public static final int actionBarItemBackground = com.kaolafm.opensdk.R.attr.actionBarItemBackground;
    public static final int actionBarPopupTheme = com.kaolafm.opensdk.R.attr.actionBarPopupTheme;
    public static final int actionBarSize = com.kaolafm.opensdk.R.attr.actionBarSize;
    public static final int actionBarSplitStyle = com.kaolafm.opensdk.R.attr.actionBarSplitStyle;
    public static final int actionBarStyle = com.kaolafm.opensdk.R.attr.actionBarStyle;
    public static final int actionBarTabBarStyle = com.kaolafm.opensdk.R.attr.actionBarTabBarStyle;
    public static final int actionBarTabStyle = com.kaolafm.opensdk.R.attr.actionBarTabStyle;
    public static final int actionBarTabTextStyle = com.kaolafm.opensdk.R.attr.actionBarTabTextStyle;
    public static final int actionBarTheme = com.kaolafm.opensdk.R.attr.actionBarTheme;
    public static final int actionBarWidgetTheme = com.kaolafm.opensdk.R.attr.actionBarWidgetTheme;
    public static final int actionButtonStyle = com.kaolafm.opensdk.R.attr.actionButtonStyle;
    public static final int actionDropDownStyle = com.kaolafm.opensdk.R.attr.actionDropDownStyle;
    public static final int actionLayout = com.kaolafm.opensdk.R.attr.actionLayout;
    public static final int actionMenuTextAppearance = com.kaolafm.opensdk.R.attr.actionMenuTextAppearance;
    public static final int actionMenuTextColor = com.kaolafm.opensdk.R.attr.actionMenuTextColor;
    public static final int actionModeBackground = com.kaolafm.opensdk.R.attr.actionModeBackground;
    public static final int actionModeCloseButtonStyle = com.kaolafm.opensdk.R.attr.actionModeCloseButtonStyle;
    public static final int actionModeCloseDrawable = com.kaolafm.opensdk.R.attr.actionModeCloseDrawable;
    public static final int actionModeCopyDrawable = com.kaolafm.opensdk.R.attr.actionModeCopyDrawable;
    public static final int actionModeCutDrawable = com.kaolafm.opensdk.R.attr.actionModeCutDrawable;
    public static final int actionModeFindDrawable = com.kaolafm.opensdk.R.attr.actionModeFindDrawable;
    public static final int actionModePasteDrawable = com.kaolafm.opensdk.R.attr.actionModePasteDrawable;
    public static final int actionModePopupWindowStyle = com.kaolafm.opensdk.R.attr.actionModePopupWindowStyle;
    public static final int actionModeSelectAllDrawable = com.kaolafm.opensdk.R.attr.actionModeSelectAllDrawable;
    public static final int actionModeShareDrawable = com.kaolafm.opensdk.R.attr.actionModeShareDrawable;
    public static final int actionModeSplitBackground = com.kaolafm.opensdk.R.attr.actionModeSplitBackground;
    public static final int actionModeStyle = com.kaolafm.opensdk.R.attr.actionModeStyle;
    public static final int actionModeWebSearchDrawable = com.kaolafm.opensdk.R.attr.actionModeWebSearchDrawable;
    public static final int actionOverflowButtonStyle = com.kaolafm.opensdk.R.attr.actionOverflowButtonStyle;
    public static final int actionOverflowMenuStyle = com.kaolafm.opensdk.R.attr.actionOverflowMenuStyle;
    public static final int actionProviderClass = com.kaolafm.opensdk.R.attr.actionProviderClass;
    public static final int actionViewClass = com.kaolafm.opensdk.R.attr.actionViewClass;
    public static final int activityChooserViewStyle = com.kaolafm.opensdk.R.attr.activityChooserViewStyle;
    public static final int alertDialogButtonGroupStyle = com.kaolafm.opensdk.R.attr.alertDialogButtonGroupStyle;
    public static final int alertDialogCenterButtons = com.kaolafm.opensdk.R.attr.alertDialogCenterButtons;
    public static final int alertDialogStyle = com.kaolafm.opensdk.R.attr.alertDialogStyle;
    public static final int alertDialogTheme = com.kaolafm.opensdk.R.attr.alertDialogTheme;
    public static final int allowStacking = com.kaolafm.opensdk.R.attr.allowStacking;
    public static final int alpha = com.kaolafm.opensdk.R.attr.alpha;
    public static final int alphabeticModifiers = com.kaolafm.opensdk.R.attr.alphabeticModifiers;
    public static final int arrowHeadLength = com.kaolafm.opensdk.R.attr.arrowHeadLength;
    public static final int arrowShaftLength = com.kaolafm.opensdk.R.attr.arrowShaftLength;
    public static final int autoCompleteTextViewStyle = com.kaolafm.opensdk.R.attr.autoCompleteTextViewStyle;
    public static final int autoSizeMaxTextSize = com.kaolafm.opensdk.R.attr.autoSizeMaxTextSize;
    public static final int autoSizeMinTextSize = com.kaolafm.opensdk.R.attr.autoSizeMinTextSize;
    public static final int autoSizePresetSizes = com.kaolafm.opensdk.R.attr.autoSizePresetSizes;
    public static final int autoSizeStepGranularity = com.kaolafm.opensdk.R.attr.autoSizeStepGranularity;
    public static final int autoSizeTextType = com.kaolafm.opensdk.R.attr.autoSizeTextType;
    public static final int background = com.kaolafm.opensdk.R.attr.background;
    public static final int backgroundSplit = com.kaolafm.opensdk.R.attr.backgroundSplit;
    public static final int backgroundStacked = com.kaolafm.opensdk.R.attr.backgroundStacked;
    public static final int backgroundTint = com.kaolafm.opensdk.R.attr.backgroundTint;
    public static final int backgroundTintMode = com.kaolafm.opensdk.R.attr.backgroundTintMode;
    public static final int barLength = com.kaolafm.opensdk.R.attr.barLength;
    public static final int borderlessButtonStyle = com.kaolafm.opensdk.R.attr.borderlessButtonStyle;
    public static final int buttonBarButtonStyle = com.kaolafm.opensdk.R.attr.buttonBarButtonStyle;
    public static final int buttonBarNegativeButtonStyle = com.kaolafm.opensdk.R.attr.buttonBarNegativeButtonStyle;
    public static final int buttonBarNeutralButtonStyle = com.kaolafm.opensdk.R.attr.buttonBarNeutralButtonStyle;
    public static final int buttonBarPositiveButtonStyle = com.kaolafm.opensdk.R.attr.buttonBarPositiveButtonStyle;
    public static final int buttonBarStyle = com.kaolafm.opensdk.R.attr.buttonBarStyle;
    public static final int buttonCompat = com.kaolafm.opensdk.R.attr.buttonCompat;
    public static final int buttonGravity = com.kaolafm.opensdk.R.attr.buttonGravity;
    public static final int buttonIconDimen = com.kaolafm.opensdk.R.attr.buttonIconDimen;
    public static final int buttonPanelSideLayout = com.kaolafm.opensdk.R.attr.buttonPanelSideLayout;
    public static final int buttonStyle = com.kaolafm.opensdk.R.attr.buttonStyle;
    public static final int buttonStyleSmall = com.kaolafm.opensdk.R.attr.buttonStyleSmall;
    public static final int buttonTint = com.kaolafm.opensdk.R.attr.buttonTint;
    public static final int buttonTintMode = com.kaolafm.opensdk.R.attr.buttonTintMode;
    public static final int checkboxStyle = com.kaolafm.opensdk.R.attr.checkboxStyle;
    public static final int checkedTextViewStyle = com.kaolafm.opensdk.R.attr.checkedTextViewStyle;
    public static final int closeIcon = com.kaolafm.opensdk.R.attr.closeIcon;
    public static final int closeItemLayout = com.kaolafm.opensdk.R.attr.closeItemLayout;
    public static final int collapseContentDescription = com.kaolafm.opensdk.R.attr.collapseContentDescription;
    public static final int collapseIcon = com.kaolafm.opensdk.R.attr.collapseIcon;
    public static final int color = com.kaolafm.opensdk.R.attr.color;
    public static final int colorAccent = com.kaolafm.opensdk.R.attr.colorAccent;
    public static final int colorBackgroundFloating = com.kaolafm.opensdk.R.attr.colorBackgroundFloating;
    public static final int colorButtonNormal = com.kaolafm.opensdk.R.attr.colorButtonNormal;
    public static final int colorControlActivated = com.kaolafm.opensdk.R.attr.colorControlActivated;
    public static final int colorControlHighlight = com.kaolafm.opensdk.R.attr.colorControlHighlight;
    public static final int colorControlNormal = com.kaolafm.opensdk.R.attr.colorControlNormal;
    public static final int colorError = com.kaolafm.opensdk.R.attr.colorError;
    public static final int colorPrimary = com.kaolafm.opensdk.R.attr.colorPrimary;
    public static final int colorPrimaryDark = com.kaolafm.opensdk.R.attr.colorPrimaryDark;
    public static final int colorSwitchThumbNormal = com.kaolafm.opensdk.R.attr.colorSwitchThumbNormal;
    public static final int commitIcon = com.kaolafm.opensdk.R.attr.commitIcon;
    public static final int contentDescription = com.kaolafm.opensdk.R.attr.contentDescription;
    public static final int contentInsetEnd = com.kaolafm.opensdk.R.attr.contentInsetEnd;
    public static final int contentInsetEndWithActions = com.kaolafm.opensdk.R.attr.contentInsetEndWithActions;
    public static final int contentInsetLeft = com.kaolafm.opensdk.R.attr.contentInsetLeft;
    public static final int contentInsetRight = com.kaolafm.opensdk.R.attr.contentInsetRight;
    public static final int contentInsetStart = com.kaolafm.opensdk.R.attr.contentInsetStart;
    public static final int contentInsetStartWithNavigation = com.kaolafm.opensdk.R.attr.contentInsetStartWithNavigation;
    public static final int controlBackground = com.kaolafm.opensdk.R.attr.controlBackground;
    public static final int customNavigationLayout = com.kaolafm.opensdk.R.attr.customNavigationLayout;
    public static final int defaultQueryHint = com.kaolafm.opensdk.R.attr.defaultQueryHint;
    public static final int dialogCornerRadius = com.kaolafm.opensdk.R.attr.dialogCornerRadius;
    public static final int dialogPreferredPadding = com.kaolafm.opensdk.R.attr.dialogPreferredPadding;
    public static final int dialogTheme = com.kaolafm.opensdk.R.attr.dialogTheme;
    public static final int displayOptions = com.kaolafm.opensdk.R.attr.displayOptions;
    public static final int divider = com.kaolafm.opensdk.R.attr.divider;
    public static final int dividerHorizontal = com.kaolafm.opensdk.R.attr.dividerHorizontal;
    public static final int dividerPadding = com.kaolafm.opensdk.R.attr.dividerPadding;
    public static final int dividerVertical = com.kaolafm.opensdk.R.attr.dividerVertical;
    public static final int drawableBottomCompat = com.kaolafm.opensdk.R.attr.drawableBottomCompat;
    public static final int drawableEndCompat = com.kaolafm.opensdk.R.attr.drawableEndCompat;
    public static final int drawableLeftCompat = com.kaolafm.opensdk.R.attr.drawableLeftCompat;
    public static final int drawableRightCompat = com.kaolafm.opensdk.R.attr.drawableRightCompat;
    public static final int drawableSize = com.kaolafm.opensdk.R.attr.drawableSize;
    public static final int drawableStartCompat = com.kaolafm.opensdk.R.attr.drawableStartCompat;
    public static final int drawableTint = com.kaolafm.opensdk.R.attr.drawableTint;
    public static final int drawableTintMode = com.kaolafm.opensdk.R.attr.drawableTintMode;
    public static final int drawableTopCompat = com.kaolafm.opensdk.R.attr.drawableTopCompat;
    public static final int drawerArrowStyle = com.kaolafm.opensdk.R.attr.drawerArrowStyle;
    public static final int dropDownListViewStyle = com.kaolafm.opensdk.R.attr.dropDownListViewStyle;
    public static final int dropdownListPreferredItemHeight = com.kaolafm.opensdk.R.attr.dropdownListPreferredItemHeight;
    public static final int editTextBackground = com.kaolafm.opensdk.R.attr.editTextBackground;
    public static final int editTextColor = com.kaolafm.opensdk.R.attr.editTextColor;
    public static final int editTextStyle = com.kaolafm.opensdk.R.attr.editTextStyle;
    public static final int elevation = com.kaolafm.opensdk.R.attr.elevation;
    public static final int expandActivityOverflowButtonDrawable = com.kaolafm.opensdk.R.attr.expandActivityOverflowButtonDrawable;
    public static final int firstBaselineToTopHeight = com.kaolafm.opensdk.R.attr.firstBaselineToTopHeight;
    public static final int font = com.kaolafm.opensdk.R.attr.font;
    public static final int fontFamily = com.kaolafm.opensdk.R.attr.fontFamily;
    public static final int fontProviderAuthority = com.kaolafm.opensdk.R.attr.fontProviderAuthority;
    public static final int fontProviderCerts = com.kaolafm.opensdk.R.attr.fontProviderCerts;
    public static final int fontProviderFetchStrategy = com.kaolafm.opensdk.R.attr.fontProviderFetchStrategy;
    public static final int fontProviderFetchTimeout = com.kaolafm.opensdk.R.attr.fontProviderFetchTimeout;
    public static final int fontProviderPackage = com.kaolafm.opensdk.R.attr.fontProviderPackage;
    public static final int fontProviderQuery = com.kaolafm.opensdk.R.attr.fontProviderQuery;
    public static final int fontProviderSystemFontFamily = com.kaolafm.opensdk.R.attr.fontProviderSystemFontFamily;
    public static final int fontStyle = com.kaolafm.opensdk.R.attr.fontStyle;
    public static final int fontVariationSettings = com.kaolafm.opensdk.R.attr.fontVariationSettings;
    public static final int fontWeight = com.kaolafm.opensdk.R.attr.fontWeight;
    public static final int gapBetweenBars = com.kaolafm.opensdk.R.attr.gapBetweenBars;
    public static final int goIcon = com.kaolafm.opensdk.R.attr.goIcon;
    public static final int height = com.kaolafm.opensdk.R.attr.height;
    public static final int hideOnContentScroll = com.kaolafm.opensdk.R.attr.hideOnContentScroll;
    public static final int homeAsUpIndicator = com.kaolafm.opensdk.R.attr.homeAsUpIndicator;
    public static final int homeLayout = com.kaolafm.opensdk.R.attr.homeLayout;
    public static final int icon = com.kaolafm.opensdk.R.attr.icon;
    public static final int iconTint = com.kaolafm.opensdk.R.attr.iconTint;
    public static final int iconTintMode = com.kaolafm.opensdk.R.attr.iconTintMode;
    public static final int iconifiedByDefault = com.kaolafm.opensdk.R.attr.iconifiedByDefault;
    public static final int imageButtonStyle = com.kaolafm.opensdk.R.attr.imageButtonStyle;
    public static final int indeterminateProgressStyle = com.kaolafm.opensdk.R.attr.indeterminateProgressStyle;
    public static final int initialActivityCount = com.kaolafm.opensdk.R.attr.initialActivityCount;
    public static final int isLightTheme = com.kaolafm.opensdk.R.attr.isLightTheme;
    public static final int itemPadding = com.kaolafm.opensdk.R.attr.itemPadding;
    public static final int lStar = com.kaolafm.opensdk.R.attr.lStar;
    public static final int lastBaselineToBottomHeight = com.kaolafm.opensdk.R.attr.lastBaselineToBottomHeight;
    public static final int layout = com.kaolafm.opensdk.R.attr.layout;
    public static final int lineHeight = com.kaolafm.opensdk.R.attr.lineHeight;
    public static final int listChoiceBackgroundIndicator = com.kaolafm.opensdk.R.attr.listChoiceBackgroundIndicator;
    public static final int listChoiceIndicatorMultipleAnimated = com.kaolafm.opensdk.R.attr.listChoiceIndicatorMultipleAnimated;
    public static final int listChoiceIndicatorSingleAnimated = com.kaolafm.opensdk.R.attr.listChoiceIndicatorSingleAnimated;
    public static final int listDividerAlertDialog = com.kaolafm.opensdk.R.attr.listDividerAlertDialog;
    public static final int listItemLayout = com.kaolafm.opensdk.R.attr.listItemLayout;
    public static final int listLayout = com.kaolafm.opensdk.R.attr.listLayout;
    public static final int listMenuViewStyle = com.kaolafm.opensdk.R.attr.listMenuViewStyle;
    public static final int listPopupWindowStyle = com.kaolafm.opensdk.R.attr.listPopupWindowStyle;
    public static final int listPreferredItemHeight = com.kaolafm.opensdk.R.attr.listPreferredItemHeight;
    public static final int listPreferredItemHeightLarge = com.kaolafm.opensdk.R.attr.listPreferredItemHeightLarge;
    public static final int listPreferredItemHeightSmall = com.kaolafm.opensdk.R.attr.listPreferredItemHeightSmall;
    public static final int listPreferredItemPaddingEnd = com.kaolafm.opensdk.R.attr.listPreferredItemPaddingEnd;
    public static final int listPreferredItemPaddingLeft = com.kaolafm.opensdk.R.attr.listPreferredItemPaddingLeft;
    public static final int listPreferredItemPaddingRight = com.kaolafm.opensdk.R.attr.listPreferredItemPaddingRight;
    public static final int listPreferredItemPaddingStart = com.kaolafm.opensdk.R.attr.listPreferredItemPaddingStart;
    public static final int logo = com.kaolafm.opensdk.R.attr.logo;
    public static final int logoDescription = com.kaolafm.opensdk.R.attr.logoDescription;
    public static final int maxButtonHeight = com.kaolafm.opensdk.R.attr.maxButtonHeight;
    public static final int measureWithLargestChild = com.kaolafm.opensdk.R.attr.measureWithLargestChild;
    public static final int menu = com.kaolafm.opensdk.R.attr.menu;
    public static final int multiChoiceItemLayout = com.kaolafm.opensdk.R.attr.multiChoiceItemLayout;
    public static final int navigationContentDescription = com.kaolafm.opensdk.R.attr.navigationContentDescription;
    public static final int navigationIcon = com.kaolafm.opensdk.R.attr.navigationIcon;
    public static final int navigationMode = com.kaolafm.opensdk.R.attr.navigationMode;
    public static final int nestedScrollViewStyle = com.kaolafm.opensdk.R.attr.nestedScrollViewStyle;
    public static final int numericModifiers = com.kaolafm.opensdk.R.attr.numericModifiers;
    public static final int overlapAnchor = com.kaolafm.opensdk.R.attr.overlapAnchor;
    public static final int paddingBottomNoButtons = com.kaolafm.opensdk.R.attr.paddingBottomNoButtons;
    public static final int paddingEnd = com.kaolafm.opensdk.R.attr.paddingEnd;
    public static final int paddingStart = com.kaolafm.opensdk.R.attr.paddingStart;
    public static final int paddingTopNoTitle = com.kaolafm.opensdk.R.attr.paddingTopNoTitle;
    public static final int panelBackground = com.kaolafm.opensdk.R.attr.panelBackground;
    public static final int panelMenuListTheme = com.kaolafm.opensdk.R.attr.panelMenuListTheme;
    public static final int panelMenuListWidth = com.kaolafm.opensdk.R.attr.panelMenuListWidth;
    public static final int popupMenuStyle = com.kaolafm.opensdk.R.attr.popupMenuStyle;
    public static final int popupTheme = com.kaolafm.opensdk.R.attr.popupTheme;
    public static final int popupWindowStyle = com.kaolafm.opensdk.R.attr.popupWindowStyle;
    public static final int preserveIconSpacing = com.kaolafm.opensdk.R.attr.preserveIconSpacing;
    public static final int progressBarPadding = com.kaolafm.opensdk.R.attr.progressBarPadding;
    public static final int progressBarStyle = com.kaolafm.opensdk.R.attr.progressBarStyle;
    public static final int queryBackground = com.kaolafm.opensdk.R.attr.queryBackground;
    public static final int queryHint = com.kaolafm.opensdk.R.attr.queryHint;
    public static final int queryPatterns = com.kaolafm.opensdk.R.attr.queryPatterns;
    public static final int radioButtonStyle = com.kaolafm.opensdk.R.attr.radioButtonStyle;
    public static final int ratingBarStyle = com.kaolafm.opensdk.R.attr.ratingBarStyle;
    public static final int ratingBarStyleIndicator = com.kaolafm.opensdk.R.attr.ratingBarStyleIndicator;
    public static final int ratingBarStyleSmall = com.kaolafm.opensdk.R.attr.ratingBarStyleSmall;
    public static final int searchHintIcon = com.kaolafm.opensdk.R.attr.searchHintIcon;
    public static final int searchIcon = com.kaolafm.opensdk.R.attr.searchIcon;
    public static final int searchViewStyle = com.kaolafm.opensdk.R.attr.searchViewStyle;
    public static final int seekBarStyle = com.kaolafm.opensdk.R.attr.seekBarStyle;
    public static final int selectableItemBackground = com.kaolafm.opensdk.R.attr.selectableItemBackground;
    public static final int selectableItemBackgroundBorderless = com.kaolafm.opensdk.R.attr.selectableItemBackgroundBorderless;
    public static final int shortcutMatchRequired = com.kaolafm.opensdk.R.attr.shortcutMatchRequired;
    public static final int showAsAction = com.kaolafm.opensdk.R.attr.showAsAction;
    public static final int showDividers = com.kaolafm.opensdk.R.attr.showDividers;
    public static final int showText = com.kaolafm.opensdk.R.attr.showText;
    public static final int showTitle = com.kaolafm.opensdk.R.attr.showTitle;
    public static final int singleChoiceItemLayout = com.kaolafm.opensdk.R.attr.singleChoiceItemLayout;
    public static final int spinBars = com.kaolafm.opensdk.R.attr.spinBars;
    public static final int spinnerDropDownItemStyle = com.kaolafm.opensdk.R.attr.spinnerDropDownItemStyle;
    public static final int spinnerStyle = com.kaolafm.opensdk.R.attr.spinnerStyle;
    public static final int splitTrack = com.kaolafm.opensdk.R.attr.splitTrack;
    public static final int srcCompat = com.kaolafm.opensdk.R.attr.srcCompat;
    public static final int state_above_anchor = com.kaolafm.opensdk.R.attr.state_above_anchor;
    public static final int subMenuArrow = com.kaolafm.opensdk.R.attr.subMenuArrow;
    public static final int submitBackground = com.kaolafm.opensdk.R.attr.submitBackground;
    public static final int subtitle = com.kaolafm.opensdk.R.attr.subtitle;
    public static final int subtitleTextAppearance = com.kaolafm.opensdk.R.attr.subtitleTextAppearance;
    public static final int subtitleTextColor = com.kaolafm.opensdk.R.attr.subtitleTextColor;
    public static final int subtitleTextStyle = com.kaolafm.opensdk.R.attr.subtitleTextStyle;
    public static final int suggestionRowLayout = com.kaolafm.opensdk.R.attr.suggestionRowLayout;
    public static final int switchMinWidth = com.kaolafm.opensdk.R.attr.switchMinWidth;
    public static final int switchPadding = com.kaolafm.opensdk.R.attr.switchPadding;
    public static final int switchStyle = com.kaolafm.opensdk.R.attr.switchStyle;
    public static final int switchTextAppearance = com.kaolafm.opensdk.R.attr.switchTextAppearance;
    public static final int textAllCaps = com.kaolafm.opensdk.R.attr.textAllCaps;
    public static final int textAppearanceLargePopupMenu = com.kaolafm.opensdk.R.attr.textAppearanceLargePopupMenu;
    public static final int textAppearanceListItem = com.kaolafm.opensdk.R.attr.textAppearanceListItem;
    public static final int textAppearanceListItemSecondary = com.kaolafm.opensdk.R.attr.textAppearanceListItemSecondary;
    public static final int textAppearanceListItemSmall = com.kaolafm.opensdk.R.attr.textAppearanceListItemSmall;
    public static final int textAppearancePopupMenuHeader = com.kaolafm.opensdk.R.attr.textAppearancePopupMenuHeader;
    public static final int textAppearanceSearchResultSubtitle = com.kaolafm.opensdk.R.attr.textAppearanceSearchResultSubtitle;
    public static final int textAppearanceSearchResultTitle = com.kaolafm.opensdk.R.attr.textAppearanceSearchResultTitle;
    public static final int textAppearanceSmallPopupMenu = com.kaolafm.opensdk.R.attr.textAppearanceSmallPopupMenu;
    public static final int textColorAlertDialogListItem = com.kaolafm.opensdk.R.attr.textColorAlertDialogListItem;
    public static final int textColorSearchUrl = com.kaolafm.opensdk.R.attr.textColorSearchUrl;
    public static final int textLocale = com.kaolafm.opensdk.R.attr.textLocale;
    public static final int theme = com.kaolafm.opensdk.R.attr.theme;
    public static final int thickness = com.kaolafm.opensdk.R.attr.thickness;
    public static final int thumbTextPadding = com.kaolafm.opensdk.R.attr.thumbTextPadding;
    public static final int thumbTint = com.kaolafm.opensdk.R.attr.thumbTint;
    public static final int thumbTintMode = com.kaolafm.opensdk.R.attr.thumbTintMode;
    public static final int tickMark = com.kaolafm.opensdk.R.attr.tickMark;
    public static final int tickMarkTint = com.kaolafm.opensdk.R.attr.tickMarkTint;
    public static final int tickMarkTintMode = com.kaolafm.opensdk.R.attr.tickMarkTintMode;
    public static final int tint = com.kaolafm.opensdk.R.attr.tint;
    public static final int tintMode = com.kaolafm.opensdk.R.attr.tintMode;
    public static final int title = com.kaolafm.opensdk.R.attr.title;
    public static final int titleMargin = com.kaolafm.opensdk.R.attr.titleMargin;
    public static final int titleMarginBottom = com.kaolafm.opensdk.R.attr.titleMarginBottom;
    public static final int titleMarginEnd = com.kaolafm.opensdk.R.attr.titleMarginEnd;
    public static final int titleMarginStart = com.kaolafm.opensdk.R.attr.titleMarginStart;
    public static final int titleMarginTop = com.kaolafm.opensdk.R.attr.titleMarginTop;
    public static final int titleMargins = com.kaolafm.opensdk.R.attr.titleMargins;
    public static final int titleTextAppearance = com.kaolafm.opensdk.R.attr.titleTextAppearance;
    public static final int titleTextColor = com.kaolafm.opensdk.R.attr.titleTextColor;
    public static final int titleTextStyle = com.kaolafm.opensdk.R.attr.titleTextStyle;
    public static final int toolbarNavigationButtonStyle = com.kaolafm.opensdk.R.attr.toolbarNavigationButtonStyle;
    public static final int toolbarStyle = com.kaolafm.opensdk.R.attr.toolbarStyle;
    public static final int tooltipForegroundColor = com.kaolafm.opensdk.R.attr.tooltipForegroundColor;
    public static final int tooltipFrameBackground = com.kaolafm.opensdk.R.attr.tooltipFrameBackground;
    public static final int tooltipText = com.kaolafm.opensdk.R.attr.tooltipText;
    public static final int track = com.kaolafm.opensdk.R.attr.track;
    public static final int trackTint = com.kaolafm.opensdk.R.attr.trackTint;
    public static final int trackTintMode = com.kaolafm.opensdk.R.attr.trackTintMode;
    public static final int ttcIndex = com.kaolafm.opensdk.R.attr.ttcIndex;
    public static final int viewInflaterClass = com.kaolafm.opensdk.R.attr.viewInflaterClass;
    public static final int voiceIcon = com.kaolafm.opensdk.R.attr.voiceIcon;
    public static final int windowActionBar = com.kaolafm.opensdk.R.attr.windowActionBar;
    public static final int windowActionBarOverlay = com.kaolafm.opensdk.R.attr.windowActionBarOverlay;
    public static final int windowActionModeOverlay = com.kaolafm.opensdk.R.attr.windowActionModeOverlay;
    public static final int windowFixedHeightMajor = com.kaolafm.opensdk.R.attr.windowFixedHeightMajor;
    public static final int windowFixedHeightMinor = com.kaolafm.opensdk.R.attr.windowFixedHeightMinor;
    public static final int windowFixedWidthMajor = com.kaolafm.opensdk.R.attr.windowFixedWidthMajor;
    public static final int windowFixedWidthMinor = com.kaolafm.opensdk.R.attr.windowFixedWidthMinor;
    public static final int windowMinWidthMajor = com.kaolafm.opensdk.R.attr.windowMinWidthMajor;
    public static final int windowMinWidthMinor = com.kaolafm.opensdk.R.attr.windowMinWidthMinor;
    public static final int windowNoTitle = com.kaolafm.opensdk.R.attr.windowNoTitle;
    }
  public static final class bool {
    public static final int abc_action_bar_embed_tabs = com.kaolafm.opensdk.R.bool.abc_action_bar_embed_tabs;
    public static final int abc_allow_stacked_button_bar = com.kaolafm.opensdk.R.bool.abc_allow_stacked_button_bar;
    public static final int abc_config_actionMenuItemAllCaps = com.kaolafm.opensdk.R.bool.abc_config_actionMenuItemAllCaps;
    }
  public static final class color {
    public static final int abc_background_cache_hint_selector_material_dark = com.kaolafm.opensdk.R.color.abc_background_cache_hint_selector_material_dark;
    public static final int abc_background_cache_hint_selector_material_light = com.kaolafm.opensdk.R.color.abc_background_cache_hint_selector_material_light;
    public static final int abc_btn_colored_borderless_text_material = com.kaolafm.opensdk.R.color.abc_btn_colored_borderless_text_material;
    public static final int abc_btn_colored_text_material = com.kaolafm.opensdk.R.color.abc_btn_colored_text_material;
    public static final int abc_color_highlight_material = com.kaolafm.opensdk.R.color.abc_color_highlight_material;
    public static final int abc_decor_view_status_guard = com.kaolafm.opensdk.R.color.abc_decor_view_status_guard;
    public static final int abc_decor_view_status_guard_light = com.kaolafm.opensdk.R.color.abc_decor_view_status_guard_light;
    public static final int abc_hint_foreground_material_dark = com.kaolafm.opensdk.R.color.abc_hint_foreground_material_dark;
    public static final int abc_hint_foreground_material_light = com.kaolafm.opensdk.R.color.abc_hint_foreground_material_light;
    public static final int abc_input_method_navigation_guard = com.kaolafm.opensdk.R.color.abc_input_method_navigation_guard;
    public static final int abc_primary_text_disable_only_material_dark = com.kaolafm.opensdk.R.color.abc_primary_text_disable_only_material_dark;
    public static final int abc_primary_text_disable_only_material_light = com.kaolafm.opensdk.R.color.abc_primary_text_disable_only_material_light;
    public static final int abc_primary_text_material_dark = com.kaolafm.opensdk.R.color.abc_primary_text_material_dark;
    public static final int abc_primary_text_material_light = com.kaolafm.opensdk.R.color.abc_primary_text_material_light;
    public static final int abc_search_url_text = com.kaolafm.opensdk.R.color.abc_search_url_text;
    public static final int abc_search_url_text_normal = com.kaolafm.opensdk.R.color.abc_search_url_text_normal;
    public static final int abc_search_url_text_pressed = com.kaolafm.opensdk.R.color.abc_search_url_text_pressed;
    public static final int abc_search_url_text_selected = com.kaolafm.opensdk.R.color.abc_search_url_text_selected;
    public static final int abc_secondary_text_material_dark = com.kaolafm.opensdk.R.color.abc_secondary_text_material_dark;
    public static final int abc_secondary_text_material_light = com.kaolafm.opensdk.R.color.abc_secondary_text_material_light;
    public static final int abc_tint_btn_checkable = com.kaolafm.opensdk.R.color.abc_tint_btn_checkable;
    public static final int abc_tint_default = com.kaolafm.opensdk.R.color.abc_tint_default;
    public static final int abc_tint_edittext = com.kaolafm.opensdk.R.color.abc_tint_edittext;
    public static final int abc_tint_seek_thumb = com.kaolafm.opensdk.R.color.abc_tint_seek_thumb;
    public static final int abc_tint_spinner = com.kaolafm.opensdk.R.color.abc_tint_spinner;
    public static final int abc_tint_switch_track = com.kaolafm.opensdk.R.color.abc_tint_switch_track;
    public static final int accent_material_dark = com.kaolafm.opensdk.R.color.accent_material_dark;
    public static final int accent_material_light = com.kaolafm.opensdk.R.color.accent_material_light;
    public static final int androidx_core_ripple_material_light = com.kaolafm.opensdk.R.color.androidx_core_ripple_material_light;
    public static final int androidx_core_secondary_text_default_material_light = com.kaolafm.opensdk.R.color.androidx_core_secondary_text_default_material_light;
    public static final int background_floating_material_dark = com.kaolafm.opensdk.R.color.background_floating_material_dark;
    public static final int background_floating_material_light = com.kaolafm.opensdk.R.color.background_floating_material_light;
    public static final int background_material_dark = com.kaolafm.opensdk.R.color.background_material_dark;
    public static final int background_material_light = com.kaolafm.opensdk.R.color.background_material_light;
    public static final int bright_foreground_disabled_material_dark = com.kaolafm.opensdk.R.color.bright_foreground_disabled_material_dark;
    public static final int bright_foreground_disabled_material_light = com.kaolafm.opensdk.R.color.bright_foreground_disabled_material_light;
    public static final int bright_foreground_inverse_material_dark = com.kaolafm.opensdk.R.color.bright_foreground_inverse_material_dark;
    public static final int bright_foreground_inverse_material_light = com.kaolafm.opensdk.R.color.bright_foreground_inverse_material_light;
    public static final int bright_foreground_material_dark = com.kaolafm.opensdk.R.color.bright_foreground_material_dark;
    public static final int bright_foreground_material_light = com.kaolafm.opensdk.R.color.bright_foreground_material_light;
    public static final int button_material_dark = com.kaolafm.opensdk.R.color.button_material_dark;
    public static final int button_material_light = com.kaolafm.opensdk.R.color.button_material_light;
    public static final int colorhahaha = com.kaolafm.opensdk.R.color.colorhahaha;
    public static final int dim_foreground_disabled_material_dark = com.kaolafm.opensdk.R.color.dim_foreground_disabled_material_dark;
    public static final int dim_foreground_disabled_material_light = com.kaolafm.opensdk.R.color.dim_foreground_disabled_material_light;
    public static final int dim_foreground_material_dark = com.kaolafm.opensdk.R.color.dim_foreground_material_dark;
    public static final int dim_foreground_material_light = com.kaolafm.opensdk.R.color.dim_foreground_material_light;
    public static final int error_color_material_dark = com.kaolafm.opensdk.R.color.error_color_material_dark;
    public static final int error_color_material_light = com.kaolafm.opensdk.R.color.error_color_material_light;
    public static final int foreground_material_dark = com.kaolafm.opensdk.R.color.foreground_material_dark;
    public static final int foreground_material_light = com.kaolafm.opensdk.R.color.foreground_material_light;
    public static final int highlighted_text_material_dark = com.kaolafm.opensdk.R.color.highlighted_text_material_dark;
    public static final int highlighted_text_material_light = com.kaolafm.opensdk.R.color.highlighted_text_material_light;
    public static final int material_blue_grey_800 = com.kaolafm.opensdk.R.color.material_blue_grey_800;
    public static final int material_blue_grey_900 = com.kaolafm.opensdk.R.color.material_blue_grey_900;
    public static final int material_blue_grey_950 = com.kaolafm.opensdk.R.color.material_blue_grey_950;
    public static final int material_deep_teal_200 = com.kaolafm.opensdk.R.color.material_deep_teal_200;
    public static final int material_deep_teal_500 = com.kaolafm.opensdk.R.color.material_deep_teal_500;
    public static final int material_grey_100 = com.kaolafm.opensdk.R.color.material_grey_100;
    public static final int material_grey_300 = com.kaolafm.opensdk.R.color.material_grey_300;
    public static final int material_grey_50 = com.kaolafm.opensdk.R.color.material_grey_50;
    public static final int material_grey_600 = com.kaolafm.opensdk.R.color.material_grey_600;
    public static final int material_grey_800 = com.kaolafm.opensdk.R.color.material_grey_800;
    public static final int material_grey_850 = com.kaolafm.opensdk.R.color.material_grey_850;
    public static final int material_grey_900 = com.kaolafm.opensdk.R.color.material_grey_900;
    public static final int notification_action_color_filter = com.kaolafm.opensdk.R.color.notification_action_color_filter;
    public static final int notification_icon_bg_color = com.kaolafm.opensdk.R.color.notification_icon_bg_color;
    public static final int primary_dark_material_dark = com.kaolafm.opensdk.R.color.primary_dark_material_dark;
    public static final int primary_dark_material_light = com.kaolafm.opensdk.R.color.primary_dark_material_light;
    public static final int primary_material_dark = com.kaolafm.opensdk.R.color.primary_material_dark;
    public static final int primary_material_light = com.kaolafm.opensdk.R.color.primary_material_light;
    public static final int primary_text_default_material_dark = com.kaolafm.opensdk.R.color.primary_text_default_material_dark;
    public static final int primary_text_default_material_light = com.kaolafm.opensdk.R.color.primary_text_default_material_light;
    public static final int primary_text_disabled_material_dark = com.kaolafm.opensdk.R.color.primary_text_disabled_material_dark;
    public static final int primary_text_disabled_material_light = com.kaolafm.opensdk.R.color.primary_text_disabled_material_light;
    public static final int ripple_material_dark = com.kaolafm.opensdk.R.color.ripple_material_dark;
    public static final int ripple_material_light = com.kaolafm.opensdk.R.color.ripple_material_light;
    public static final int secondary_text_default_material_dark = com.kaolafm.opensdk.R.color.secondary_text_default_material_dark;
    public static final int secondary_text_default_material_light = com.kaolafm.opensdk.R.color.secondary_text_default_material_light;
    public static final int secondary_text_disabled_material_dark = com.kaolafm.opensdk.R.color.secondary_text_disabled_material_dark;
    public static final int secondary_text_disabled_material_light = com.kaolafm.opensdk.R.color.secondary_text_disabled_material_light;
    public static final int switch_thumb_disabled_material_dark = com.kaolafm.opensdk.R.color.switch_thumb_disabled_material_dark;
    public static final int switch_thumb_disabled_material_light = com.kaolafm.opensdk.R.color.switch_thumb_disabled_material_light;
    public static final int switch_thumb_material_dark = com.kaolafm.opensdk.R.color.switch_thumb_material_dark;
    public static final int switch_thumb_material_light = com.kaolafm.opensdk.R.color.switch_thumb_material_light;
    public static final int switch_thumb_normal_material_dark = com.kaolafm.opensdk.R.color.switch_thumb_normal_material_dark;
    public static final int switch_thumb_normal_material_light = com.kaolafm.opensdk.R.color.switch_thumb_normal_material_light;
    public static final int tooltip_background_dark = com.kaolafm.opensdk.R.color.tooltip_background_dark;
    public static final int tooltip_background_light = com.kaolafm.opensdk.R.color.tooltip_background_light;
    }
  public static final class dimen {
    public static final int abc_action_bar_content_inset_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_content_inset_material;
    public static final int abc_action_bar_content_inset_with_nav = com.kaolafm.opensdk.R.dimen.abc_action_bar_content_inset_with_nav;
    public static final int abc_action_bar_default_height_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_default_height_material;
    public static final int abc_action_bar_default_padding_end_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_default_padding_end_material;
    public static final int abc_action_bar_default_padding_start_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_default_padding_start_material;
    public static final int abc_action_bar_elevation_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_elevation_material;
    public static final int abc_action_bar_icon_vertical_padding_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_icon_vertical_padding_material;
    public static final int abc_action_bar_overflow_padding_end_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_overflow_padding_end_material;
    public static final int abc_action_bar_overflow_padding_start_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_overflow_padding_start_material;
    public static final int abc_action_bar_stacked_max_height = com.kaolafm.opensdk.R.dimen.abc_action_bar_stacked_max_height;
    public static final int abc_action_bar_stacked_tab_max_width = com.kaolafm.opensdk.R.dimen.abc_action_bar_stacked_tab_max_width;
    public static final int abc_action_bar_subtitle_bottom_margin_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_subtitle_bottom_margin_material;
    public static final int abc_action_bar_subtitle_top_margin_material = com.kaolafm.opensdk.R.dimen.abc_action_bar_subtitle_top_margin_material;
    public static final int abc_action_button_min_height_material = com.kaolafm.opensdk.R.dimen.abc_action_button_min_height_material;
    public static final int abc_action_button_min_width_material = com.kaolafm.opensdk.R.dimen.abc_action_button_min_width_material;
    public static final int abc_action_button_min_width_overflow_material = com.kaolafm.opensdk.R.dimen.abc_action_button_min_width_overflow_material;
    public static final int abc_alert_dialog_button_bar_height = com.kaolafm.opensdk.R.dimen.abc_alert_dialog_button_bar_height;
    public static final int abc_alert_dialog_button_dimen = com.kaolafm.opensdk.R.dimen.abc_alert_dialog_button_dimen;
    public static final int abc_button_inset_horizontal_material = com.kaolafm.opensdk.R.dimen.abc_button_inset_horizontal_material;
    public static final int abc_button_inset_vertical_material = com.kaolafm.opensdk.R.dimen.abc_button_inset_vertical_material;
    public static final int abc_button_padding_horizontal_material = com.kaolafm.opensdk.R.dimen.abc_button_padding_horizontal_material;
    public static final int abc_button_padding_vertical_material = com.kaolafm.opensdk.R.dimen.abc_button_padding_vertical_material;
    public static final int abc_cascading_menus_min_smallest_width = com.kaolafm.opensdk.R.dimen.abc_cascading_menus_min_smallest_width;
    public static final int abc_config_prefDialogWidth = com.kaolafm.opensdk.R.dimen.abc_config_prefDialogWidth;
    public static final int abc_control_corner_material = com.kaolafm.opensdk.R.dimen.abc_control_corner_material;
    public static final int abc_control_inset_material = com.kaolafm.opensdk.R.dimen.abc_control_inset_material;
    public static final int abc_control_padding_material = com.kaolafm.opensdk.R.dimen.abc_control_padding_material;
    public static final int abc_dialog_corner_radius_material = com.kaolafm.opensdk.R.dimen.abc_dialog_corner_radius_material;
    public static final int abc_dialog_fixed_height_major = com.kaolafm.opensdk.R.dimen.abc_dialog_fixed_height_major;
    public static final int abc_dialog_fixed_height_minor = com.kaolafm.opensdk.R.dimen.abc_dialog_fixed_height_minor;
    public static final int abc_dialog_fixed_width_major = com.kaolafm.opensdk.R.dimen.abc_dialog_fixed_width_major;
    public static final int abc_dialog_fixed_width_minor = com.kaolafm.opensdk.R.dimen.abc_dialog_fixed_width_minor;
    public static final int abc_dialog_list_padding_bottom_no_buttons = com.kaolafm.opensdk.R.dimen.abc_dialog_list_padding_bottom_no_buttons;
    public static final int abc_dialog_list_padding_top_no_title = com.kaolafm.opensdk.R.dimen.abc_dialog_list_padding_top_no_title;
    public static final int abc_dialog_min_width_major = com.kaolafm.opensdk.R.dimen.abc_dialog_min_width_major;
    public static final int abc_dialog_min_width_minor = com.kaolafm.opensdk.R.dimen.abc_dialog_min_width_minor;
    public static final int abc_dialog_padding_material = com.kaolafm.opensdk.R.dimen.abc_dialog_padding_material;
    public static final int abc_dialog_padding_top_material = com.kaolafm.opensdk.R.dimen.abc_dialog_padding_top_material;
    public static final int abc_dialog_title_divider_material = com.kaolafm.opensdk.R.dimen.abc_dialog_title_divider_material;
    public static final int abc_disabled_alpha_material_dark = com.kaolafm.opensdk.R.dimen.abc_disabled_alpha_material_dark;
    public static final int abc_disabled_alpha_material_light = com.kaolafm.opensdk.R.dimen.abc_disabled_alpha_material_light;
    public static final int abc_dropdownitem_icon_width = com.kaolafm.opensdk.R.dimen.abc_dropdownitem_icon_width;
    public static final int abc_dropdownitem_text_padding_left = com.kaolafm.opensdk.R.dimen.abc_dropdownitem_text_padding_left;
    public static final int abc_dropdownitem_text_padding_right = com.kaolafm.opensdk.R.dimen.abc_dropdownitem_text_padding_right;
    public static final int abc_edit_text_inset_bottom_material = com.kaolafm.opensdk.R.dimen.abc_edit_text_inset_bottom_material;
    public static final int abc_edit_text_inset_horizontal_material = com.kaolafm.opensdk.R.dimen.abc_edit_text_inset_horizontal_material;
    public static final int abc_edit_text_inset_top_material = com.kaolafm.opensdk.R.dimen.abc_edit_text_inset_top_material;
    public static final int abc_floating_window_z = com.kaolafm.opensdk.R.dimen.abc_floating_window_z;
    public static final int abc_list_item_height_large_material = com.kaolafm.opensdk.R.dimen.abc_list_item_height_large_material;
    public static final int abc_list_item_height_material = com.kaolafm.opensdk.R.dimen.abc_list_item_height_material;
    public static final int abc_list_item_height_small_material = com.kaolafm.opensdk.R.dimen.abc_list_item_height_small_material;
    public static final int abc_list_item_padding_horizontal_material = com.kaolafm.opensdk.R.dimen.abc_list_item_padding_horizontal_material;
    public static final int abc_panel_menu_list_width = com.kaolafm.opensdk.R.dimen.abc_panel_menu_list_width;
    public static final int abc_progress_bar_height_material = com.kaolafm.opensdk.R.dimen.abc_progress_bar_height_material;
    public static final int abc_search_view_preferred_height = com.kaolafm.opensdk.R.dimen.abc_search_view_preferred_height;
    public static final int abc_search_view_preferred_width = com.kaolafm.opensdk.R.dimen.abc_search_view_preferred_width;
    public static final int abc_seekbar_track_background_height_material = com.kaolafm.opensdk.R.dimen.abc_seekbar_track_background_height_material;
    public static final int abc_seekbar_track_progress_height_material = com.kaolafm.opensdk.R.dimen.abc_seekbar_track_progress_height_material;
    public static final int abc_select_dialog_padding_start_material = com.kaolafm.opensdk.R.dimen.abc_select_dialog_padding_start_material;
    public static final int abc_switch_padding = com.kaolafm.opensdk.R.dimen.abc_switch_padding;
    public static final int abc_text_size_body_1_material = com.kaolafm.opensdk.R.dimen.abc_text_size_body_1_material;
    public static final int abc_text_size_body_2_material = com.kaolafm.opensdk.R.dimen.abc_text_size_body_2_material;
    public static final int abc_text_size_button_material = com.kaolafm.opensdk.R.dimen.abc_text_size_button_material;
    public static final int abc_text_size_caption_material = com.kaolafm.opensdk.R.dimen.abc_text_size_caption_material;
    public static final int abc_text_size_display_1_material = com.kaolafm.opensdk.R.dimen.abc_text_size_display_1_material;
    public static final int abc_text_size_display_2_material = com.kaolafm.opensdk.R.dimen.abc_text_size_display_2_material;
    public static final int abc_text_size_display_3_material = com.kaolafm.opensdk.R.dimen.abc_text_size_display_3_material;
    public static final int abc_text_size_display_4_material = com.kaolafm.opensdk.R.dimen.abc_text_size_display_4_material;
    public static final int abc_text_size_headline_material = com.kaolafm.opensdk.R.dimen.abc_text_size_headline_material;
    public static final int abc_text_size_large_material = com.kaolafm.opensdk.R.dimen.abc_text_size_large_material;
    public static final int abc_text_size_medium_material = com.kaolafm.opensdk.R.dimen.abc_text_size_medium_material;
    public static final int abc_text_size_menu_header_material = com.kaolafm.opensdk.R.dimen.abc_text_size_menu_header_material;
    public static final int abc_text_size_menu_material = com.kaolafm.opensdk.R.dimen.abc_text_size_menu_material;
    public static final int abc_text_size_small_material = com.kaolafm.opensdk.R.dimen.abc_text_size_small_material;
    public static final int abc_text_size_subhead_material = com.kaolafm.opensdk.R.dimen.abc_text_size_subhead_material;
    public static final int abc_text_size_subtitle_material_toolbar = com.kaolafm.opensdk.R.dimen.abc_text_size_subtitle_material_toolbar;
    public static final int abc_text_size_title_material = com.kaolafm.opensdk.R.dimen.abc_text_size_title_material;
    public static final int abc_text_size_title_material_toolbar = com.kaolafm.opensdk.R.dimen.abc_text_size_title_material_toolbar;
    public static final int compat_button_inset_horizontal_material = com.kaolafm.opensdk.R.dimen.compat_button_inset_horizontal_material;
    public static final int compat_button_inset_vertical_material = com.kaolafm.opensdk.R.dimen.compat_button_inset_vertical_material;
    public static final int compat_button_padding_horizontal_material = com.kaolafm.opensdk.R.dimen.compat_button_padding_horizontal_material;
    public static final int compat_button_padding_vertical_material = com.kaolafm.opensdk.R.dimen.compat_button_padding_vertical_material;
    public static final int compat_control_corner_material = com.kaolafm.opensdk.R.dimen.compat_control_corner_material;
    public static final int compat_notification_large_icon_max_height = com.kaolafm.opensdk.R.dimen.compat_notification_large_icon_max_height;
    public static final int compat_notification_large_icon_max_width = com.kaolafm.opensdk.R.dimen.compat_notification_large_icon_max_width;
    public static final int disabled_alpha_material_dark = com.kaolafm.opensdk.R.dimen.disabled_alpha_material_dark;
    public static final int disabled_alpha_material_light = com.kaolafm.opensdk.R.dimen.disabled_alpha_material_light;
    public static final int highlight_alpha_material_colored = com.kaolafm.opensdk.R.dimen.highlight_alpha_material_colored;
    public static final int highlight_alpha_material_dark = com.kaolafm.opensdk.R.dimen.highlight_alpha_material_dark;
    public static final int highlight_alpha_material_light = com.kaolafm.opensdk.R.dimen.highlight_alpha_material_light;
    public static final int hint_alpha_material_dark = com.kaolafm.opensdk.R.dimen.hint_alpha_material_dark;
    public static final int hint_alpha_material_light = com.kaolafm.opensdk.R.dimen.hint_alpha_material_light;
    public static final int hint_pressed_alpha_material_dark = com.kaolafm.opensdk.R.dimen.hint_pressed_alpha_material_dark;
    public static final int hint_pressed_alpha_material_light = com.kaolafm.opensdk.R.dimen.hint_pressed_alpha_material_light;
    public static final int notification_action_icon_size = com.kaolafm.opensdk.R.dimen.notification_action_icon_size;
    public static final int notification_action_text_size = com.kaolafm.opensdk.R.dimen.notification_action_text_size;
    public static final int notification_big_circle_margin = com.kaolafm.opensdk.R.dimen.notification_big_circle_margin;
    public static final int notification_content_margin_start = com.kaolafm.opensdk.R.dimen.notification_content_margin_start;
    public static final int notification_large_icon_height = com.kaolafm.opensdk.R.dimen.notification_large_icon_height;
    public static final int notification_large_icon_width = com.kaolafm.opensdk.R.dimen.notification_large_icon_width;
    public static final int notification_main_column_padding_top = com.kaolafm.opensdk.R.dimen.notification_main_column_padding_top;
    public static final int notification_media_narrow_margin = com.kaolafm.opensdk.R.dimen.notification_media_narrow_margin;
    public static final int notification_right_icon_size = com.kaolafm.opensdk.R.dimen.notification_right_icon_size;
    public static final int notification_right_side_padding_top = com.kaolafm.opensdk.R.dimen.notification_right_side_padding_top;
    public static final int notification_small_icon_background_padding = com.kaolafm.opensdk.R.dimen.notification_small_icon_background_padding;
    public static final int notification_small_icon_size_as_large = com.kaolafm.opensdk.R.dimen.notification_small_icon_size_as_large;
    public static final int notification_subtext_size = com.kaolafm.opensdk.R.dimen.notification_subtext_size;
    public static final int notification_top_pad = com.kaolafm.opensdk.R.dimen.notification_top_pad;
    public static final int notification_top_pad_large_text = com.kaolafm.opensdk.R.dimen.notification_top_pad_large_text;
    public static final int tooltip_corner_radius = com.kaolafm.opensdk.R.dimen.tooltip_corner_radius;
    public static final int tooltip_horizontal_padding = com.kaolafm.opensdk.R.dimen.tooltip_horizontal_padding;
    public static final int tooltip_margin = com.kaolafm.opensdk.R.dimen.tooltip_margin;
    public static final int tooltip_precise_anchor_extra_offset = com.kaolafm.opensdk.R.dimen.tooltip_precise_anchor_extra_offset;
    public static final int tooltip_precise_anchor_threshold = com.kaolafm.opensdk.R.dimen.tooltip_precise_anchor_threshold;
    public static final int tooltip_vertical_padding = com.kaolafm.opensdk.R.dimen.tooltip_vertical_padding;
    public static final int tooltip_y_offset_non_touch = com.kaolafm.opensdk.R.dimen.tooltip_y_offset_non_touch;
    public static final int tooltip_y_offset_touch = com.kaolafm.opensdk.R.dimen.tooltip_y_offset_touch;
    }
  public static final class drawable {
    public static final int abc_ab_share_pack_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_ab_share_pack_mtrl_alpha;
    public static final int abc_action_bar_item_background_material = com.kaolafm.opensdk.R.drawable.abc_action_bar_item_background_material;
    public static final int abc_btn_borderless_material = com.kaolafm.opensdk.R.drawable.abc_btn_borderless_material;
    public static final int abc_btn_check_material = com.kaolafm.opensdk.R.drawable.abc_btn_check_material;
    public static final int abc_btn_check_material_anim = com.kaolafm.opensdk.R.drawable.abc_btn_check_material_anim;
    public static final int abc_btn_check_to_on_mtrl_000 = com.kaolafm.opensdk.R.drawable.abc_btn_check_to_on_mtrl_000;
    public static final int abc_btn_check_to_on_mtrl_015 = com.kaolafm.opensdk.R.drawable.abc_btn_check_to_on_mtrl_015;
    public static final int abc_btn_colored_material = com.kaolafm.opensdk.R.drawable.abc_btn_colored_material;
    public static final int abc_btn_default_mtrl_shape = com.kaolafm.opensdk.R.drawable.abc_btn_default_mtrl_shape;
    public static final int abc_btn_radio_material = com.kaolafm.opensdk.R.drawable.abc_btn_radio_material;
    public static final int abc_btn_radio_material_anim = com.kaolafm.opensdk.R.drawable.abc_btn_radio_material_anim;
    public static final int abc_btn_radio_to_on_mtrl_000 = com.kaolafm.opensdk.R.drawable.abc_btn_radio_to_on_mtrl_000;
    public static final int abc_btn_radio_to_on_mtrl_015 = com.kaolafm.opensdk.R.drawable.abc_btn_radio_to_on_mtrl_015;
    public static final int abc_btn_switch_to_on_mtrl_00001 = com.kaolafm.opensdk.R.drawable.abc_btn_switch_to_on_mtrl_00001;
    public static final int abc_btn_switch_to_on_mtrl_00012 = com.kaolafm.opensdk.R.drawable.abc_btn_switch_to_on_mtrl_00012;
    public static final int abc_cab_background_internal_bg = com.kaolafm.opensdk.R.drawable.abc_cab_background_internal_bg;
    public static final int abc_cab_background_top_material = com.kaolafm.opensdk.R.drawable.abc_cab_background_top_material;
    public static final int abc_cab_background_top_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_cab_background_top_mtrl_alpha;
    public static final int abc_control_background_material = com.kaolafm.opensdk.R.drawable.abc_control_background_material;
    public static final int abc_dialog_material_background = com.kaolafm.opensdk.R.drawable.abc_dialog_material_background;
    public static final int abc_edit_text_material = com.kaolafm.opensdk.R.drawable.abc_edit_text_material;
    public static final int abc_ic_ab_back_material = com.kaolafm.opensdk.R.drawable.abc_ic_ab_back_material;
    public static final int abc_ic_arrow_drop_right_black_24dp = com.kaolafm.opensdk.R.drawable.abc_ic_arrow_drop_right_black_24dp;
    public static final int abc_ic_clear_material = com.kaolafm.opensdk.R.drawable.abc_ic_clear_material;
    public static final int abc_ic_commit_search_api_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_commit_search_api_mtrl_alpha;
    public static final int abc_ic_go_search_api_material = com.kaolafm.opensdk.R.drawable.abc_ic_go_search_api_material;
    public static final int abc_ic_menu_copy_mtrl_am_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_menu_copy_mtrl_am_alpha;
    public static final int abc_ic_menu_cut_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_menu_cut_mtrl_alpha;
    public static final int abc_ic_menu_overflow_material = com.kaolafm.opensdk.R.drawable.abc_ic_menu_overflow_material;
    public static final int abc_ic_menu_paste_mtrl_am_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_menu_paste_mtrl_am_alpha;
    public static final int abc_ic_menu_selectall_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_menu_selectall_mtrl_alpha;
    public static final int abc_ic_menu_share_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_ic_menu_share_mtrl_alpha;
    public static final int abc_ic_search_api_material = com.kaolafm.opensdk.R.drawable.abc_ic_search_api_material;
    public static final int abc_ic_star_black_16dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_black_16dp;
    public static final int abc_ic_star_black_36dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_black_36dp;
    public static final int abc_ic_star_black_48dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_black_48dp;
    public static final int abc_ic_star_half_black_16dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_half_black_16dp;
    public static final int abc_ic_star_half_black_36dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_half_black_36dp;
    public static final int abc_ic_star_half_black_48dp = com.kaolafm.opensdk.R.drawable.abc_ic_star_half_black_48dp;
    public static final int abc_ic_voice_search_api_material = com.kaolafm.opensdk.R.drawable.abc_ic_voice_search_api_material;
    public static final int abc_item_background_holo_dark = com.kaolafm.opensdk.R.drawable.abc_item_background_holo_dark;
    public static final int abc_item_background_holo_light = com.kaolafm.opensdk.R.drawable.abc_item_background_holo_light;
    public static final int abc_list_divider_material = com.kaolafm.opensdk.R.drawable.abc_list_divider_material;
    public static final int abc_list_divider_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_list_divider_mtrl_alpha;
    public static final int abc_list_focused_holo = com.kaolafm.opensdk.R.drawable.abc_list_focused_holo;
    public static final int abc_list_longpressed_holo = com.kaolafm.opensdk.R.drawable.abc_list_longpressed_holo;
    public static final int abc_list_pressed_holo_dark = com.kaolafm.opensdk.R.drawable.abc_list_pressed_holo_dark;
    public static final int abc_list_pressed_holo_light = com.kaolafm.opensdk.R.drawable.abc_list_pressed_holo_light;
    public static final int abc_list_selector_background_transition_holo_dark = com.kaolafm.opensdk.R.drawable.abc_list_selector_background_transition_holo_dark;
    public static final int abc_list_selector_background_transition_holo_light = com.kaolafm.opensdk.R.drawable.abc_list_selector_background_transition_holo_light;
    public static final int abc_list_selector_disabled_holo_dark = com.kaolafm.opensdk.R.drawable.abc_list_selector_disabled_holo_dark;
    public static final int abc_list_selector_disabled_holo_light = com.kaolafm.opensdk.R.drawable.abc_list_selector_disabled_holo_light;
    public static final int abc_list_selector_holo_dark = com.kaolafm.opensdk.R.drawable.abc_list_selector_holo_dark;
    public static final int abc_list_selector_holo_light = com.kaolafm.opensdk.R.drawable.abc_list_selector_holo_light;
    public static final int abc_menu_hardkey_panel_mtrl_mult = com.kaolafm.opensdk.R.drawable.abc_menu_hardkey_panel_mtrl_mult;
    public static final int abc_popup_background_mtrl_mult = com.kaolafm.opensdk.R.drawable.abc_popup_background_mtrl_mult;
    public static final int abc_ratingbar_indicator_material = com.kaolafm.opensdk.R.drawable.abc_ratingbar_indicator_material;
    public static final int abc_ratingbar_material = com.kaolafm.opensdk.R.drawable.abc_ratingbar_material;
    public static final int abc_ratingbar_small_material = com.kaolafm.opensdk.R.drawable.abc_ratingbar_small_material;
    public static final int abc_scrubber_control_off_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_scrubber_control_off_mtrl_alpha;
    public static final int abc_scrubber_control_to_pressed_mtrl_000 = com.kaolafm.opensdk.R.drawable.abc_scrubber_control_to_pressed_mtrl_000;
    public static final int abc_scrubber_control_to_pressed_mtrl_005 = com.kaolafm.opensdk.R.drawable.abc_scrubber_control_to_pressed_mtrl_005;
    public static final int abc_scrubber_primary_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_scrubber_primary_mtrl_alpha;
    public static final int abc_scrubber_track_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_scrubber_track_mtrl_alpha;
    public static final int abc_seekbar_thumb_material = com.kaolafm.opensdk.R.drawable.abc_seekbar_thumb_material;
    public static final int abc_seekbar_tick_mark_material = com.kaolafm.opensdk.R.drawable.abc_seekbar_tick_mark_material;
    public static final int abc_seekbar_track_material = com.kaolafm.opensdk.R.drawable.abc_seekbar_track_material;
    public static final int abc_spinner_mtrl_am_alpha = com.kaolafm.opensdk.R.drawable.abc_spinner_mtrl_am_alpha;
    public static final int abc_spinner_textfield_background_material = com.kaolafm.opensdk.R.drawable.abc_spinner_textfield_background_material;
    public static final int abc_switch_thumb_material = com.kaolafm.opensdk.R.drawable.abc_switch_thumb_material;
    public static final int abc_switch_track_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_switch_track_mtrl_alpha;
    public static final int abc_tab_indicator_material = com.kaolafm.opensdk.R.drawable.abc_tab_indicator_material;
    public static final int abc_tab_indicator_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_tab_indicator_mtrl_alpha;
    public static final int abc_text_cursor_material = com.kaolafm.opensdk.R.drawable.abc_text_cursor_material;
    public static final int abc_text_select_handle_left_mtrl_dark = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_left_mtrl_dark;
    public static final int abc_text_select_handle_left_mtrl_light = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_left_mtrl_light;
    public static final int abc_text_select_handle_middle_mtrl_dark = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_middle_mtrl_dark;
    public static final int abc_text_select_handle_middle_mtrl_light = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_middle_mtrl_light;
    public static final int abc_text_select_handle_right_mtrl_dark = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_right_mtrl_dark;
    public static final int abc_text_select_handle_right_mtrl_light = com.kaolafm.opensdk.R.drawable.abc_text_select_handle_right_mtrl_light;
    public static final int abc_textfield_activated_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_textfield_activated_mtrl_alpha;
    public static final int abc_textfield_default_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_textfield_default_mtrl_alpha;
    public static final int abc_textfield_search_activated_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_textfield_search_activated_mtrl_alpha;
    public static final int abc_textfield_search_default_mtrl_alpha = com.kaolafm.opensdk.R.drawable.abc_textfield_search_default_mtrl_alpha;
    public static final int abc_textfield_search_material = com.kaolafm.opensdk.R.drawable.abc_textfield_search_material;
    public static final int abc_vector_test = com.kaolafm.opensdk.R.drawable.abc_vector_test;
    public static final int btn_checkbox_checked_mtrl = com.kaolafm.opensdk.R.drawable.btn_checkbox_checked_mtrl;
    public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = com.kaolafm.opensdk.R.drawable.btn_checkbox_checked_to_unchecked_mtrl_animation;
    public static final int btn_checkbox_unchecked_mtrl = com.kaolafm.opensdk.R.drawable.btn_checkbox_unchecked_mtrl;
    public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = com.kaolafm.opensdk.R.drawable.btn_checkbox_unchecked_to_checked_mtrl_animation;
    public static final int btn_radio_off_mtrl = com.kaolafm.opensdk.R.drawable.btn_radio_off_mtrl;
    public static final int btn_radio_off_to_on_mtrl_animation = com.kaolafm.opensdk.R.drawable.btn_radio_off_to_on_mtrl_animation;
    public static final int btn_radio_on_mtrl = com.kaolafm.opensdk.R.drawable.btn_radio_on_mtrl;
    public static final int btn_radio_on_to_off_mtrl_animation = com.kaolafm.opensdk.R.drawable.btn_radio_on_to_off_mtrl_animation;
    public static final int notification_action_background = com.kaolafm.opensdk.R.drawable.notification_action_background;
    public static final int notification_bg = com.kaolafm.opensdk.R.drawable.notification_bg;
    public static final int notification_bg_low = com.kaolafm.opensdk.R.drawable.notification_bg_low;
    public static final int notification_bg_low_normal = com.kaolafm.opensdk.R.drawable.notification_bg_low_normal;
    public static final int notification_bg_low_pressed = com.kaolafm.opensdk.R.drawable.notification_bg_low_pressed;
    public static final int notification_bg_normal = com.kaolafm.opensdk.R.drawable.notification_bg_normal;
    public static final int notification_bg_normal_pressed = com.kaolafm.opensdk.R.drawable.notification_bg_normal_pressed;
    public static final int notification_icon_background = com.kaolafm.opensdk.R.drawable.notification_icon_background;
    public static final int notification_template_icon_bg = com.kaolafm.opensdk.R.drawable.notification_template_icon_bg;
    public static final int notification_template_icon_low_bg = com.kaolafm.opensdk.R.drawable.notification_template_icon_low_bg;
    public static final int notification_tile_bg = com.kaolafm.opensdk.R.drawable.notification_tile_bg;
    public static final int notify_panel_notification_icon_bg = com.kaolafm.opensdk.R.drawable.notify_panel_notification_icon_bg;
    public static final int tooltip_frame_dark = com.kaolafm.opensdk.R.drawable.tooltip_frame_dark;
    public static final int tooltip_frame_light = com.kaolafm.opensdk.R.drawable.tooltip_frame_light;
    }
  public static final class id {
    public static final int accessibility_action_clickable_span = com.kaolafm.opensdk.R.id.accessibility_action_clickable_span;
    public static final int accessibility_custom_action_0 = com.kaolafm.opensdk.R.id.accessibility_custom_action_0;
    public static final int accessibility_custom_action_1 = com.kaolafm.opensdk.R.id.accessibility_custom_action_1;
    public static final int accessibility_custom_action_10 = com.kaolafm.opensdk.R.id.accessibility_custom_action_10;
    public static final int accessibility_custom_action_11 = com.kaolafm.opensdk.R.id.accessibility_custom_action_11;
    public static final int accessibility_custom_action_12 = com.kaolafm.opensdk.R.id.accessibility_custom_action_12;
    public static final int accessibility_custom_action_13 = com.kaolafm.opensdk.R.id.accessibility_custom_action_13;
    public static final int accessibility_custom_action_14 = com.kaolafm.opensdk.R.id.accessibility_custom_action_14;
    public static final int accessibility_custom_action_15 = com.kaolafm.opensdk.R.id.accessibility_custom_action_15;
    public static final int accessibility_custom_action_16 = com.kaolafm.opensdk.R.id.accessibility_custom_action_16;
    public static final int accessibility_custom_action_17 = com.kaolafm.opensdk.R.id.accessibility_custom_action_17;
    public static final int accessibility_custom_action_18 = com.kaolafm.opensdk.R.id.accessibility_custom_action_18;
    public static final int accessibility_custom_action_19 = com.kaolafm.opensdk.R.id.accessibility_custom_action_19;
    public static final int accessibility_custom_action_2 = com.kaolafm.opensdk.R.id.accessibility_custom_action_2;
    public static final int accessibility_custom_action_20 = com.kaolafm.opensdk.R.id.accessibility_custom_action_20;
    public static final int accessibility_custom_action_21 = com.kaolafm.opensdk.R.id.accessibility_custom_action_21;
    public static final int accessibility_custom_action_22 = com.kaolafm.opensdk.R.id.accessibility_custom_action_22;
    public static final int accessibility_custom_action_23 = com.kaolafm.opensdk.R.id.accessibility_custom_action_23;
    public static final int accessibility_custom_action_24 = com.kaolafm.opensdk.R.id.accessibility_custom_action_24;
    public static final int accessibility_custom_action_25 = com.kaolafm.opensdk.R.id.accessibility_custom_action_25;
    public static final int accessibility_custom_action_26 = com.kaolafm.opensdk.R.id.accessibility_custom_action_26;
    public static final int accessibility_custom_action_27 = com.kaolafm.opensdk.R.id.accessibility_custom_action_27;
    public static final int accessibility_custom_action_28 = com.kaolafm.opensdk.R.id.accessibility_custom_action_28;
    public static final int accessibility_custom_action_29 = com.kaolafm.opensdk.R.id.accessibility_custom_action_29;
    public static final int accessibility_custom_action_3 = com.kaolafm.opensdk.R.id.accessibility_custom_action_3;
    public static final int accessibility_custom_action_30 = com.kaolafm.opensdk.R.id.accessibility_custom_action_30;
    public static final int accessibility_custom_action_31 = com.kaolafm.opensdk.R.id.accessibility_custom_action_31;
    public static final int accessibility_custom_action_4 = com.kaolafm.opensdk.R.id.accessibility_custom_action_4;
    public static final int accessibility_custom_action_5 = com.kaolafm.opensdk.R.id.accessibility_custom_action_5;
    public static final int accessibility_custom_action_6 = com.kaolafm.opensdk.R.id.accessibility_custom_action_6;
    public static final int accessibility_custom_action_7 = com.kaolafm.opensdk.R.id.accessibility_custom_action_7;
    public static final int accessibility_custom_action_8 = com.kaolafm.opensdk.R.id.accessibility_custom_action_8;
    public static final int accessibility_custom_action_9 = com.kaolafm.opensdk.R.id.accessibility_custom_action_9;
    public static final int action_bar = com.kaolafm.opensdk.R.id.action_bar;
    public static final int action_bar_activity_content = com.kaolafm.opensdk.R.id.action_bar_activity_content;
    public static final int action_bar_container = com.kaolafm.opensdk.R.id.action_bar_container;
    public static final int action_bar_root = com.kaolafm.opensdk.R.id.action_bar_root;
    public static final int action_bar_spinner = com.kaolafm.opensdk.R.id.action_bar_spinner;
    public static final int action_bar_subtitle = com.kaolafm.opensdk.R.id.action_bar_subtitle;
    public static final int action_bar_title = com.kaolafm.opensdk.R.id.action_bar_title;
    public static final int action_container = com.kaolafm.opensdk.R.id.action_container;
    public static final int action_context_bar = com.kaolafm.opensdk.R.id.action_context_bar;
    public static final int action_divider = com.kaolafm.opensdk.R.id.action_divider;
    public static final int action_image = com.kaolafm.opensdk.R.id.action_image;
    public static final int action_menu_divider = com.kaolafm.opensdk.R.id.action_menu_divider;
    public static final int action_menu_presenter = com.kaolafm.opensdk.R.id.action_menu_presenter;
    public static final int action_mode_bar = com.kaolafm.opensdk.R.id.action_mode_bar;
    public static final int action_mode_bar_stub = com.kaolafm.opensdk.R.id.action_mode_bar_stub;
    public static final int action_mode_close_button = com.kaolafm.opensdk.R.id.action_mode_close_button;
    public static final int action_text = com.kaolafm.opensdk.R.id.action_text;
    public static final int actions = com.kaolafm.opensdk.R.id.actions;
    public static final int activity_chooser_view_content = com.kaolafm.opensdk.R.id.activity_chooser_view_content;
    public static final int add = com.kaolafm.opensdk.R.id.add;
    public static final int alertTitle = com.kaolafm.opensdk.R.id.alertTitle;
    public static final int async = com.kaolafm.opensdk.R.id.async;
    public static final int blocking = com.kaolafm.opensdk.R.id.blocking;
    public static final int buttonPanel = com.kaolafm.opensdk.R.id.buttonPanel;
    public static final int checkbox = com.kaolafm.opensdk.R.id.checkbox;
    public static final int checked = com.kaolafm.opensdk.R.id.checked;
    public static final int chronometer = com.kaolafm.opensdk.R.id.chronometer;
    public static final int content = com.kaolafm.opensdk.R.id.content;
    public static final int contentPanel = com.kaolafm.opensdk.R.id.contentPanel;
    public static final int custom = com.kaolafm.opensdk.R.id.custom;
    public static final int customPanel = com.kaolafm.opensdk.R.id.customPanel;
    public static final int decor_content_parent = com.kaolafm.opensdk.R.id.decor_content_parent;
    public static final int default_activity_button = com.kaolafm.opensdk.R.id.default_activity_button;
    public static final int dialog_button = com.kaolafm.opensdk.R.id.dialog_button;
    public static final int edit_query = com.kaolafm.opensdk.R.id.edit_query;
    public static final int expand_activities_button = com.kaolafm.opensdk.R.id.expand_activities_button;
    public static final int expanded_menu = com.kaolafm.opensdk.R.id.expanded_menu;
    public static final int forever = com.kaolafm.opensdk.R.id.forever;
    public static final int fragment_container_view_tag = com.kaolafm.opensdk.R.id.fragment_container_view_tag;
    public static final int group_divider = com.kaolafm.opensdk.R.id.group_divider;
    public static final int home = com.kaolafm.opensdk.R.id.home;
    public static final int icon = com.kaolafm.opensdk.R.id.icon;
    public static final int icon_group = com.kaolafm.opensdk.R.id.icon_group;
    public static final int image = com.kaolafm.opensdk.R.id.image;
    public static final int info = com.kaolafm.opensdk.R.id.info;
    public static final int italic = com.kaolafm.opensdk.R.id.italic;
    public static final int line1 = com.kaolafm.opensdk.R.id.line1;
    public static final int line3 = com.kaolafm.opensdk.R.id.line3;
    public static final int listMode = com.kaolafm.opensdk.R.id.listMode;
    public static final int list_item = com.kaolafm.opensdk.R.id.list_item;
    public static final int message = com.kaolafm.opensdk.R.id.message;
    public static final int multiply = com.kaolafm.opensdk.R.id.multiply;
    public static final int none = com.kaolafm.opensdk.R.id.none;
    public static final int normal = com.kaolafm.opensdk.R.id.normal;
    public static final int notification_background = com.kaolafm.opensdk.R.id.notification_background;
    public static final int notification_main_column = com.kaolafm.opensdk.R.id.notification_main_column;
    public static final int notification_main_column_container = com.kaolafm.opensdk.R.id.notification_main_column_container;
    public static final int off = com.kaolafm.opensdk.R.id.off;
    public static final int on = com.kaolafm.opensdk.R.id.on;
    public static final int parentPanel = com.kaolafm.opensdk.R.id.parentPanel;
    public static final int progress_circular = com.kaolafm.opensdk.R.id.progress_circular;
    public static final int progress_horizontal = com.kaolafm.opensdk.R.id.progress_horizontal;
    public static final int radio = com.kaolafm.opensdk.R.id.radio;
    public static final int right_icon = com.kaolafm.opensdk.R.id.right_icon;
    public static final int right_side = com.kaolafm.opensdk.R.id.right_side;
    public static final int screen = com.kaolafm.opensdk.R.id.screen;
    public static final int scrollIndicatorDown = com.kaolafm.opensdk.R.id.scrollIndicatorDown;
    public static final int scrollIndicatorUp = com.kaolafm.opensdk.R.id.scrollIndicatorUp;
    public static final int scrollView = com.kaolafm.opensdk.R.id.scrollView;
    public static final int search_badge = com.kaolafm.opensdk.R.id.search_badge;
    public static final int search_bar = com.kaolafm.opensdk.R.id.search_bar;
    public static final int search_button = com.kaolafm.opensdk.R.id.search_button;
    public static final int search_close_btn = com.kaolafm.opensdk.R.id.search_close_btn;
    public static final int search_edit_frame = com.kaolafm.opensdk.R.id.search_edit_frame;
    public static final int search_go_btn = com.kaolafm.opensdk.R.id.search_go_btn;
    public static final int search_mag_icon = com.kaolafm.opensdk.R.id.search_mag_icon;
    public static final int search_plate = com.kaolafm.opensdk.R.id.search_plate;
    public static final int search_src_text = com.kaolafm.opensdk.R.id.search_src_text;
    public static final int search_voice_btn = com.kaolafm.opensdk.R.id.search_voice_btn;
    public static final int select_dialog_listview = com.kaolafm.opensdk.R.id.select_dialog_listview;
    public static final int shortcut = com.kaolafm.opensdk.R.id.shortcut;
    public static final int spacer = com.kaolafm.opensdk.R.id.spacer;
    public static final int split_action_bar = com.kaolafm.opensdk.R.id.split_action_bar;
    public static final int src_atop = com.kaolafm.opensdk.R.id.src_atop;
    public static final int src_in = com.kaolafm.opensdk.R.id.src_in;
    public static final int src_over = com.kaolafm.opensdk.R.id.src_over;
    public static final int submenuarrow = com.kaolafm.opensdk.R.id.submenuarrow;
    public static final int submit_area = com.kaolafm.opensdk.R.id.submit_area;
    public static final int tabMode = com.kaolafm.opensdk.R.id.tabMode;
    public static final int tag_accessibility_actions = com.kaolafm.opensdk.R.id.tag_accessibility_actions;
    public static final int tag_accessibility_clickable_spans = com.kaolafm.opensdk.R.id.tag_accessibility_clickable_spans;
    public static final int tag_accessibility_heading = com.kaolafm.opensdk.R.id.tag_accessibility_heading;
    public static final int tag_accessibility_pane_title = com.kaolafm.opensdk.R.id.tag_accessibility_pane_title;
    public static final int tag_on_apply_window_listener = com.kaolafm.opensdk.R.id.tag_on_apply_window_listener;
    public static final int tag_on_receive_content_listener = com.kaolafm.opensdk.R.id.tag_on_receive_content_listener;
    public static final int tag_on_receive_content_mime_types = com.kaolafm.opensdk.R.id.tag_on_receive_content_mime_types;
    public static final int tag_screen_reader_focusable = com.kaolafm.opensdk.R.id.tag_screen_reader_focusable;
    public static final int tag_state_description = com.kaolafm.opensdk.R.id.tag_state_description;
    public static final int tag_transition_group = com.kaolafm.opensdk.R.id.tag_transition_group;
    public static final int tag_unhandled_key_event_manager = com.kaolafm.opensdk.R.id.tag_unhandled_key_event_manager;
    public static final int tag_unhandled_key_listeners = com.kaolafm.opensdk.R.id.tag_unhandled_key_listeners;
    public static final int tag_window_insets_animation_callback = com.kaolafm.opensdk.R.id.tag_window_insets_animation_callback;
    public static final int text = com.kaolafm.opensdk.R.id.text;
    public static final int text2 = com.kaolafm.opensdk.R.id.text2;
    public static final int textSpacerNoButtons = com.kaolafm.opensdk.R.id.textSpacerNoButtons;
    public static final int textSpacerNoTitle = com.kaolafm.opensdk.R.id.textSpacerNoTitle;
    public static final int time = com.kaolafm.opensdk.R.id.time;
    public static final int title = com.kaolafm.opensdk.R.id.title;
    public static final int titleDividerNoCustom = com.kaolafm.opensdk.R.id.titleDividerNoCustom;
    public static final int title_template = com.kaolafm.opensdk.R.id.title_template;
    public static final int topPanel = com.kaolafm.opensdk.R.id.topPanel;
    public static final int unchecked = com.kaolafm.opensdk.R.id.unchecked;
    public static final int uniform = com.kaolafm.opensdk.R.id.uniform;
    public static final int up = com.kaolafm.opensdk.R.id.up;
    public static final int view_tree_lifecycle_owner = com.kaolafm.opensdk.R.id.view_tree_lifecycle_owner;
    public static final int visible_removing_fragment_view_tag = com.kaolafm.opensdk.R.id.visible_removing_fragment_view_tag;
    public static final int wrap_content = com.kaolafm.opensdk.R.id.wrap_content;
    }
  public static final class integer {
    public static final int abc_config_activityDefaultDur = com.kaolafm.opensdk.R.integer.abc_config_activityDefaultDur;
    public static final int abc_config_activityShortDur = com.kaolafm.opensdk.R.integer.abc_config_activityShortDur;
    public static final int cancel_button_image_alpha = com.kaolafm.opensdk.R.integer.cancel_button_image_alpha;
    public static final int config_tooltipAnimTime = com.kaolafm.opensdk.R.integer.config_tooltipAnimTime;
    public static final int status_bar_notification_info_maxnum = com.kaolafm.opensdk.R.integer.status_bar_notification_info_maxnum;
    }
  public static final class interpolator {
    public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = com.kaolafm.opensdk.R.interpolator.btn_checkbox_checked_mtrl_animation_interpolator_0;
    public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = com.kaolafm.opensdk.R.interpolator.btn_checkbox_checked_mtrl_animation_interpolator_1;
    public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = com.kaolafm.opensdk.R.interpolator.btn_checkbox_unchecked_mtrl_animation_interpolator_0;
    public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = com.kaolafm.opensdk.R.interpolator.btn_checkbox_unchecked_mtrl_animation_interpolator_1;
    public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = com.kaolafm.opensdk.R.interpolator.btn_radio_to_off_mtrl_animation_interpolator_0;
    public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = com.kaolafm.opensdk.R.interpolator.btn_radio_to_on_mtrl_animation_interpolator_0;
    public static final int fast_out_slow_in = com.kaolafm.opensdk.R.interpolator.fast_out_slow_in;
    }
  public static final class layout {
    public static final int abc_action_bar_title_item = com.kaolafm.opensdk.R.layout.abc_action_bar_title_item;
    public static final int abc_action_bar_up_container = com.kaolafm.opensdk.R.layout.abc_action_bar_up_container;
    public static final int abc_action_menu_item_layout = com.kaolafm.opensdk.R.layout.abc_action_menu_item_layout;
    public static final int abc_action_menu_layout = com.kaolafm.opensdk.R.layout.abc_action_menu_layout;
    public static final int abc_action_mode_bar = com.kaolafm.opensdk.R.layout.abc_action_mode_bar;
    public static final int abc_action_mode_close_item_material = com.kaolafm.opensdk.R.layout.abc_action_mode_close_item_material;
    public static final int abc_activity_chooser_view = com.kaolafm.opensdk.R.layout.abc_activity_chooser_view;
    public static final int abc_activity_chooser_view_list_item = com.kaolafm.opensdk.R.layout.abc_activity_chooser_view_list_item;
    public static final int abc_alert_dialog_button_bar_material = com.kaolafm.opensdk.R.layout.abc_alert_dialog_button_bar_material;
    public static final int abc_alert_dialog_material = com.kaolafm.opensdk.R.layout.abc_alert_dialog_material;
    public static final int abc_alert_dialog_title_material = com.kaolafm.opensdk.R.layout.abc_alert_dialog_title_material;
    public static final int abc_cascading_menu_item_layout = com.kaolafm.opensdk.R.layout.abc_cascading_menu_item_layout;
    public static final int abc_dialog_title_material = com.kaolafm.opensdk.R.layout.abc_dialog_title_material;
    public static final int abc_expanded_menu_layout = com.kaolafm.opensdk.R.layout.abc_expanded_menu_layout;
    public static final int abc_list_menu_item_checkbox = com.kaolafm.opensdk.R.layout.abc_list_menu_item_checkbox;
    public static final int abc_list_menu_item_icon = com.kaolafm.opensdk.R.layout.abc_list_menu_item_icon;
    public static final int abc_list_menu_item_layout = com.kaolafm.opensdk.R.layout.abc_list_menu_item_layout;
    public static final int abc_list_menu_item_radio = com.kaolafm.opensdk.R.layout.abc_list_menu_item_radio;
    public static final int abc_popup_menu_header_item_layout = com.kaolafm.opensdk.R.layout.abc_popup_menu_header_item_layout;
    public static final int abc_popup_menu_item_layout = com.kaolafm.opensdk.R.layout.abc_popup_menu_item_layout;
    public static final int abc_screen_content_include = com.kaolafm.opensdk.R.layout.abc_screen_content_include;
    public static final int abc_screen_simple = com.kaolafm.opensdk.R.layout.abc_screen_simple;
    public static final int abc_screen_simple_overlay_action_mode = com.kaolafm.opensdk.R.layout.abc_screen_simple_overlay_action_mode;
    public static final int abc_screen_toolbar = com.kaolafm.opensdk.R.layout.abc_screen_toolbar;
    public static final int abc_search_dropdown_item_icons_2line = com.kaolafm.opensdk.R.layout.abc_search_dropdown_item_icons_2line;
    public static final int abc_search_view = com.kaolafm.opensdk.R.layout.abc_search_view;
    public static final int abc_select_dialog_material = com.kaolafm.opensdk.R.layout.abc_select_dialog_material;
    public static final int abc_tooltip = com.kaolafm.opensdk.R.layout.abc_tooltip;
    public static final int custom_dialog = com.kaolafm.opensdk.R.layout.custom_dialog;
    public static final int notification_action = com.kaolafm.opensdk.R.layout.notification_action;
    public static final int notification_action_tombstone = com.kaolafm.opensdk.R.layout.notification_action_tombstone;
    public static final int notification_template_custom_big = com.kaolafm.opensdk.R.layout.notification_template_custom_big;
    public static final int notification_template_icon_group = com.kaolafm.opensdk.R.layout.notification_template_icon_group;
    public static final int notification_template_part_chronometer = com.kaolafm.opensdk.R.layout.notification_template_part_chronometer;
    public static final int notification_template_part_time = com.kaolafm.opensdk.R.layout.notification_template_part_time;
    public static final int select_dialog_item_material = com.kaolafm.opensdk.R.layout.select_dialog_item_material;
    public static final int select_dialog_multichoice_material = com.kaolafm.opensdk.R.layout.select_dialog_multichoice_material;
    public static final int select_dialog_singlechoice_material = com.kaolafm.opensdk.R.layout.select_dialog_singlechoice_material;
    public static final int support_simple_spinner_dropdown_item = com.kaolafm.opensdk.R.layout.support_simple_spinner_dropdown_item;
    }
  public static final class string {
    public static final int abc_action_bar_home_description = com.kaolafm.opensdk.R.string.abc_action_bar_home_description;
    public static final int abc_action_bar_up_description = com.kaolafm.opensdk.R.string.abc_action_bar_up_description;
    public static final int abc_action_menu_overflow_description = com.kaolafm.opensdk.R.string.abc_action_menu_overflow_description;
    public static final int abc_action_mode_done = com.kaolafm.opensdk.R.string.abc_action_mode_done;
    public static final int abc_activity_chooser_view_see_all = com.kaolafm.opensdk.R.string.abc_activity_chooser_view_see_all;
    public static final int abc_activitychooserview_choose_application = com.kaolafm.opensdk.R.string.abc_activitychooserview_choose_application;
    public static final int abc_capital_off = com.kaolafm.opensdk.R.string.abc_capital_off;
    public static final int abc_capital_on = com.kaolafm.opensdk.R.string.abc_capital_on;
    public static final int abc_menu_alt_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_alt_shortcut_label;
    public static final int abc_menu_ctrl_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_ctrl_shortcut_label;
    public static final int abc_menu_delete_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_delete_shortcut_label;
    public static final int abc_menu_enter_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_enter_shortcut_label;
    public static final int abc_menu_function_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_function_shortcut_label;
    public static final int abc_menu_meta_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_meta_shortcut_label;
    public static final int abc_menu_shift_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_shift_shortcut_label;
    public static final int abc_menu_space_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_space_shortcut_label;
    public static final int abc_menu_sym_shortcut_label = com.kaolafm.opensdk.R.string.abc_menu_sym_shortcut_label;
    public static final int abc_prepend_shortcut_label = com.kaolafm.opensdk.R.string.abc_prepend_shortcut_label;
    public static final int abc_search_hint = com.kaolafm.opensdk.R.string.abc_search_hint;
    public static final int abc_searchview_description_clear = com.kaolafm.opensdk.R.string.abc_searchview_description_clear;
    public static final int abc_searchview_description_query = com.kaolafm.opensdk.R.string.abc_searchview_description_query;
    public static final int abc_searchview_description_search = com.kaolafm.opensdk.R.string.abc_searchview_description_search;
    public static final int abc_searchview_description_submit = com.kaolafm.opensdk.R.string.abc_searchview_description_submit;
    public static final int abc_searchview_description_voice = com.kaolafm.opensdk.R.string.abc_searchview_description_voice;
    public static final int abc_shareactionprovider_share_with = com.kaolafm.opensdk.R.string.abc_shareactionprovider_share_with;
    public static final int abc_shareactionprovider_share_with_application = com.kaolafm.opensdk.R.string.abc_shareactionprovider_share_with_application;
    public static final int abc_toolbar_collapse_description = com.kaolafm.opensdk.R.string.abc_toolbar_collapse_description;
    public static final int exo_download_completed = com.kaolafm.opensdk.R.string.exo_download_completed;
    public static final int exo_download_description = com.kaolafm.opensdk.R.string.exo_download_description;
    public static final int exo_download_downloading = com.kaolafm.opensdk.R.string.exo_download_downloading;
    public static final int exo_download_failed = com.kaolafm.opensdk.R.string.exo_download_failed;
    public static final int exo_download_notification_channel_name = com.kaolafm.opensdk.R.string.exo_download_notification_channel_name;
    public static final int exo_download_paused = com.kaolafm.opensdk.R.string.exo_download_paused;
    public static final int exo_download_paused_for_network = com.kaolafm.opensdk.R.string.exo_download_paused_for_network;
    public static final int exo_download_paused_for_wifi = com.kaolafm.opensdk.R.string.exo_download_paused_for_wifi;
    public static final int exo_download_removing = com.kaolafm.opensdk.R.string.exo_download_removing;
    public static final int search_menu_title = com.kaolafm.opensdk.R.string.search_menu_title;
    public static final int status_bar_notification_info_overflow = com.kaolafm.opensdk.R.string.status_bar_notification_info_overflow;
    }
  public static final class style {
    public static final int AlertDialog_AppCompat = com.kaolafm.opensdk.R.style.AlertDialog_AppCompat;
    public static final int AlertDialog_AppCompat_Light = com.kaolafm.opensdk.R.style.AlertDialog_AppCompat_Light;
    public static final int Animation_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Animation_AppCompat_Dialog;
    public static final int Animation_AppCompat_DropDownUp = com.kaolafm.opensdk.R.style.Animation_AppCompat_DropDownUp;
    public static final int Animation_AppCompat_Tooltip = com.kaolafm.opensdk.R.style.Animation_AppCompat_Tooltip;
    public static final int Base_AlertDialog_AppCompat = com.kaolafm.opensdk.R.style.Base_AlertDialog_AppCompat;
    public static final int Base_AlertDialog_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_AlertDialog_AppCompat_Light;
    public static final int Base_Animation_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_Animation_AppCompat_Dialog;
    public static final int Base_Animation_AppCompat_DropDownUp = com.kaolafm.opensdk.R.style.Base_Animation_AppCompat_DropDownUp;
    public static final int Base_Animation_AppCompat_Tooltip = com.kaolafm.opensdk.R.style.Base_Animation_AppCompat_Tooltip;
    public static final int Base_DialogWindowTitleBackground_AppCompat = com.kaolafm.opensdk.R.style.Base_DialogWindowTitleBackground_AppCompat;
    public static final int Base_DialogWindowTitle_AppCompat = com.kaolafm.opensdk.R.style.Base_DialogWindowTitle_AppCompat;
    public static final int Base_TextAppearance_AppCompat = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat;
    public static final int Base_TextAppearance_AppCompat_Body1 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Body1;
    public static final int Base_TextAppearance_AppCompat_Body2 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Body2;
    public static final int Base_TextAppearance_AppCompat_Button = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Button;
    public static final int Base_TextAppearance_AppCompat_Caption = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Caption;
    public static final int Base_TextAppearance_AppCompat_Display1 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Display1;
    public static final int Base_TextAppearance_AppCompat_Display2 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Display2;
    public static final int Base_TextAppearance_AppCompat_Display3 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Display3;
    public static final int Base_TextAppearance_AppCompat_Display4 = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Display4;
    public static final int Base_TextAppearance_AppCompat_Headline = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Headline;
    public static final int Base_TextAppearance_AppCompat_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Inverse;
    public static final int Base_TextAppearance_AppCompat_Large = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Large;
    public static final int Base_TextAppearance_AppCompat_Large_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Large_Inverse;
    public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large;
    public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small;
    public static final int Base_TextAppearance_AppCompat_Medium = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Medium;
    public static final int Base_TextAppearance_AppCompat_Medium_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Medium_Inverse;
    public static final int Base_TextAppearance_AppCompat_Menu = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Menu;
    public static final int Base_TextAppearance_AppCompat_SearchResult = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_SearchResult;
    public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_SearchResult_Subtitle;
    public static final int Base_TextAppearance_AppCompat_SearchResult_Title = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_SearchResult_Title;
    public static final int Base_TextAppearance_AppCompat_Small = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Small;
    public static final int Base_TextAppearance_AppCompat_Small_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Small_Inverse;
    public static final int Base_TextAppearance_AppCompat_Subhead = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Subhead;
    public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Subhead_Inverse;
    public static final int Base_TextAppearance_AppCompat_Title = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Title;
    public static final int Base_TextAppearance_AppCompat_Title_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Title_Inverse;
    public static final int Base_TextAppearance_AppCompat_Tooltip = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Tooltip;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Menu;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Title;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle;
    public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_ActionMode_Title;
    public static final int Base_TextAppearance_AppCompat_Widget_Button = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_Button;
    public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored;
    public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_Button_Colored;
    public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_Button_Inverse;
    public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_DropDownItem;
    public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Header;
    public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Large;
    public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Small;
    public static final int Base_TextAppearance_AppCompat_Widget_Switch = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_Switch;
    public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = com.kaolafm.opensdk.R.style.Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem;
    public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = com.kaolafm.opensdk.R.style.Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item;
    public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = com.kaolafm.opensdk.R.style.Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle;
    public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = com.kaolafm.opensdk.R.style.Base_TextAppearance_Widget_AppCompat_Toolbar_Title;
    public static final int Base_ThemeOverlay_AppCompat = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat;
    public static final int Base_ThemeOverlay_AppCompat_ActionBar = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_ActionBar;
    public static final int Base_ThemeOverlay_AppCompat_Dark = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_Dark;
    public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_Dark_ActionBar;
    public static final int Base_ThemeOverlay_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_Dialog;
    public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_Dialog_Alert;
    public static final int Base_ThemeOverlay_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_ThemeOverlay_AppCompat_Light;
    public static final int Base_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat;
    public static final int Base_Theme_AppCompat_CompactMenu = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_CompactMenu;
    public static final int Base_Theme_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Dialog;
    public static final int Base_Theme_AppCompat_DialogWhenLarge = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_DialogWhenLarge;
    public static final int Base_Theme_AppCompat_Dialog_Alert = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Dialog_Alert;
    public static final int Base_Theme_AppCompat_Dialog_FixedSize = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Dialog_FixedSize;
    public static final int Base_Theme_AppCompat_Dialog_MinWidth = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Dialog_MinWidth;
    public static final int Base_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light;
    public static final int Base_Theme_AppCompat_Light_DarkActionBar = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_DarkActionBar;
    public static final int Base_Theme_AppCompat_Light_Dialog = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_Dialog;
    public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_DialogWhenLarge;
    public static final int Base_Theme_AppCompat_Light_Dialog_Alert = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_Dialog_Alert;
    public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_Dialog_FixedSize;
    public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = com.kaolafm.opensdk.R.style.Base_Theme_AppCompat_Light_Dialog_MinWidth;
    public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_V21_ThemeOverlay_AppCompat_Dialog;
    public static final int Base_V21_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V21_Theme_AppCompat;
    public static final int Base_V21_Theme_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_V21_Theme_AppCompat_Dialog;
    public static final int Base_V21_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V21_Theme_AppCompat_Light;
    public static final int Base_V21_Theme_AppCompat_Light_Dialog = com.kaolafm.opensdk.R.style.Base_V21_Theme_AppCompat_Light_Dialog;
    public static final int Base_V22_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V22_Theme_AppCompat;
    public static final int Base_V22_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V22_Theme_AppCompat_Light;
    public static final int Base_V23_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V23_Theme_AppCompat;
    public static final int Base_V23_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V23_Theme_AppCompat_Light;
    public static final int Base_V26_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V26_Theme_AppCompat;
    public static final int Base_V26_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V26_Theme_AppCompat_Light;
    public static final int Base_V26_Widget_AppCompat_Toolbar = com.kaolafm.opensdk.R.style.Base_V26_Widget_AppCompat_Toolbar;
    public static final int Base_V28_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V28_Theme_AppCompat;
    public static final int Base_V28_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V28_Theme_AppCompat_Light;
    public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_V7_ThemeOverlay_AppCompat_Dialog;
    public static final int Base_V7_Theme_AppCompat = com.kaolafm.opensdk.R.style.Base_V7_Theme_AppCompat;
    public static final int Base_V7_Theme_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Base_V7_Theme_AppCompat_Dialog;
    public static final int Base_V7_Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Base_V7_Theme_AppCompat_Light;
    public static final int Base_V7_Theme_AppCompat_Light_Dialog = com.kaolafm.opensdk.R.style.Base_V7_Theme_AppCompat_Light_Dialog;
    public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = com.kaolafm.opensdk.R.style.Base_V7_Widget_AppCompat_AutoCompleteTextView;
    public static final int Base_V7_Widget_AppCompat_EditText = com.kaolafm.opensdk.R.style.Base_V7_Widget_AppCompat_EditText;
    public static final int Base_V7_Widget_AppCompat_Toolbar = com.kaolafm.opensdk.R.style.Base_V7_Widget_AppCompat_Toolbar;
    public static final int Base_Widget_AppCompat_ActionBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionBar;
    public static final int Base_Widget_AppCompat_ActionBar_Solid = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionBar_Solid;
    public static final int Base_Widget_AppCompat_ActionBar_TabBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionBar_TabBar;
    public static final int Base_Widget_AppCompat_ActionBar_TabText = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionBar_TabText;
    public static final int Base_Widget_AppCompat_ActionBar_TabView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionBar_TabView;
    public static final int Base_Widget_AppCompat_ActionButton = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionButton;
    public static final int Base_Widget_AppCompat_ActionButton_CloseMode = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionButton_CloseMode;
    public static final int Base_Widget_AppCompat_ActionButton_Overflow = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionButton_Overflow;
    public static final int Base_Widget_AppCompat_ActionMode = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActionMode;
    public static final int Base_Widget_AppCompat_ActivityChooserView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ActivityChooserView;
    public static final int Base_Widget_AppCompat_AutoCompleteTextView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_AutoCompleteTextView;
    public static final int Base_Widget_AppCompat_Button = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button;
    public static final int Base_Widget_AppCompat_ButtonBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ButtonBar;
    public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ButtonBar_AlertDialog;
    public static final int Base_Widget_AppCompat_Button_Borderless = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button_Borderless;
    public static final int Base_Widget_AppCompat_Button_Borderless_Colored = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button_Borderless_Colored;
    public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button_ButtonBar_AlertDialog;
    public static final int Base_Widget_AppCompat_Button_Colored = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button_Colored;
    public static final int Base_Widget_AppCompat_Button_Small = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Button_Small;
    public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_CompoundButton_CheckBox;
    public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_CompoundButton_RadioButton;
    public static final int Base_Widget_AppCompat_CompoundButton_Switch = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_CompoundButton_Switch;
    public static final int Base_Widget_AppCompat_DrawerArrowToggle = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_DrawerArrowToggle;
    public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_DrawerArrowToggle_Common;
    public static final int Base_Widget_AppCompat_DropDownItem_Spinner = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_DropDownItem_Spinner;
    public static final int Base_Widget_AppCompat_EditText = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_EditText;
    public static final int Base_Widget_AppCompat_ImageButton = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ImageButton;
    public static final int Base_Widget_AppCompat_Light_ActionBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar;
    public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar_Solid;
    public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar_TabBar;
    public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar_TabText;
    public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse;
    public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_ActionBar_TabView;
    public static final int Base_Widget_AppCompat_Light_PopupMenu = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_PopupMenu;
    public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Light_PopupMenu_Overflow;
    public static final int Base_Widget_AppCompat_ListMenuView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ListMenuView;
    public static final int Base_Widget_AppCompat_ListPopupWindow = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ListPopupWindow;
    public static final int Base_Widget_AppCompat_ListView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ListView;
    public static final int Base_Widget_AppCompat_ListView_DropDown = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ListView_DropDown;
    public static final int Base_Widget_AppCompat_ListView_Menu = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ListView_Menu;
    public static final int Base_Widget_AppCompat_PopupMenu = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_PopupMenu;
    public static final int Base_Widget_AppCompat_PopupMenu_Overflow = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_PopupMenu_Overflow;
    public static final int Base_Widget_AppCompat_PopupWindow = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_PopupWindow;
    public static final int Base_Widget_AppCompat_ProgressBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ProgressBar;
    public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_ProgressBar_Horizontal;
    public static final int Base_Widget_AppCompat_RatingBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_RatingBar;
    public static final int Base_Widget_AppCompat_RatingBar_Indicator = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_RatingBar_Indicator;
    public static final int Base_Widget_AppCompat_RatingBar_Small = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_RatingBar_Small;
    public static final int Base_Widget_AppCompat_SearchView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_SearchView;
    public static final int Base_Widget_AppCompat_SearchView_ActionBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_SearchView_ActionBar;
    public static final int Base_Widget_AppCompat_SeekBar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_SeekBar;
    public static final int Base_Widget_AppCompat_SeekBar_Discrete = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_SeekBar_Discrete;
    public static final int Base_Widget_AppCompat_Spinner = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Spinner;
    public static final int Base_Widget_AppCompat_Spinner_Underlined = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Spinner_Underlined;
    public static final int Base_Widget_AppCompat_TextView = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_TextView;
    public static final int Base_Widget_AppCompat_TextView_SpinnerItem = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_TextView_SpinnerItem;
    public static final int Base_Widget_AppCompat_Toolbar = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Toolbar;
    public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = com.kaolafm.opensdk.R.style.Base_Widget_AppCompat_Toolbar_Button_Navigation;
    public static final int Platform_AppCompat = com.kaolafm.opensdk.R.style.Platform_AppCompat;
    public static final int Platform_AppCompat_Light = com.kaolafm.opensdk.R.style.Platform_AppCompat_Light;
    public static final int Platform_ThemeOverlay_AppCompat = com.kaolafm.opensdk.R.style.Platform_ThemeOverlay_AppCompat;
    public static final int Platform_ThemeOverlay_AppCompat_Dark = com.kaolafm.opensdk.R.style.Platform_ThemeOverlay_AppCompat_Dark;
    public static final int Platform_ThemeOverlay_AppCompat_Light = com.kaolafm.opensdk.R.style.Platform_ThemeOverlay_AppCompat_Light;
    public static final int Platform_V21_AppCompat = com.kaolafm.opensdk.R.style.Platform_V21_AppCompat;
    public static final int Platform_V21_AppCompat_Light = com.kaolafm.opensdk.R.style.Platform_V21_AppCompat_Light;
    public static final int Platform_V25_AppCompat = com.kaolafm.opensdk.R.style.Platform_V25_AppCompat;
    public static final int Platform_V25_AppCompat_Light = com.kaolafm.opensdk.R.style.Platform_V25_AppCompat_Light;
    public static final int Platform_Widget_AppCompat_Spinner = com.kaolafm.opensdk.R.style.Platform_Widget_AppCompat_Spinner;
    public static final int RtlOverlay_DialogWindowTitle_AppCompat = com.kaolafm.opensdk.R.style.RtlOverlay_DialogWindowTitle_AppCompat;
    public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_ActionBar_TitleItem;
    public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_DialogTitle_Icon;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Text;
    public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Title;
    public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_SearchView_MagIcon;
    public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_Search_DropDown;
    public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1;
    public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2;
    public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Query;
    public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = com.kaolafm.opensdk.R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Text;
    public static final int RtlUnderlay_Widget_AppCompat_ActionButton = com.kaolafm.opensdk.R.style.RtlUnderlay_Widget_AppCompat_ActionButton;
    public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = com.kaolafm.opensdk.R.style.RtlUnderlay_Widget_AppCompat_ActionButton_Overflow;
    public static final int TextAppearance_AppCompat = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat;
    public static final int TextAppearance_AppCompat_Body1 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Body1;
    public static final int TextAppearance_AppCompat_Body2 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Body2;
    public static final int TextAppearance_AppCompat_Button = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Button;
    public static final int TextAppearance_AppCompat_Caption = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Caption;
    public static final int TextAppearance_AppCompat_Display1 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Display1;
    public static final int TextAppearance_AppCompat_Display2 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Display2;
    public static final int TextAppearance_AppCompat_Display3 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Display3;
    public static final int TextAppearance_AppCompat_Display4 = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Display4;
    public static final int TextAppearance_AppCompat_Headline = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Headline;
    public static final int TextAppearance_AppCompat_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Inverse;
    public static final int TextAppearance_AppCompat_Large = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Large;
    public static final int TextAppearance_AppCompat_Large_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Large_Inverse;
    public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Light_SearchResult_Subtitle;
    public static final int TextAppearance_AppCompat_Light_SearchResult_Title = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Light_SearchResult_Title;
    public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Light_Widget_PopupMenu_Large;
    public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Light_Widget_PopupMenu_Small;
    public static final int TextAppearance_AppCompat_Medium = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Medium;
    public static final int TextAppearance_AppCompat_Medium_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Medium_Inverse;
    public static final int TextAppearance_AppCompat_Menu = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Menu;
    public static final int TextAppearance_AppCompat_SearchResult_Subtitle = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_SearchResult_Subtitle;
    public static final int TextAppearance_AppCompat_SearchResult_Title = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_SearchResult_Title;
    public static final int TextAppearance_AppCompat_Small = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Small;
    public static final int TextAppearance_AppCompat_Small_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Small_Inverse;
    public static final int TextAppearance_AppCompat_Subhead = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Subhead;
    public static final int TextAppearance_AppCompat_Subhead_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Subhead_Inverse;
    public static final int TextAppearance_AppCompat_Title = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Title;
    public static final int TextAppearance_AppCompat_Title_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Title_Inverse;
    public static final int TextAppearance_AppCompat_Tooltip = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Tooltip;
    public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionBar_Menu;
    public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionBar_Subtitle;
    public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse;
    public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionBar_Title;
    public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse;
    public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionMode_Subtitle;
    public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse;
    public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionMode_Title;
    public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse;
    public static final int TextAppearance_AppCompat_Widget_Button = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_Button;
    public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_Button_Borderless_Colored;
    public static final int TextAppearance_AppCompat_Widget_Button_Colored = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_Button_Colored;
    public static final int TextAppearance_AppCompat_Widget_Button_Inverse = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_Button_Inverse;
    public static final int TextAppearance_AppCompat_Widget_DropDownItem = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_DropDownItem;
    public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_PopupMenu_Header;
    public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_PopupMenu_Large;
    public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_PopupMenu_Small;
    public static final int TextAppearance_AppCompat_Widget_Switch = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_Switch;
    public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = com.kaolafm.opensdk.R.style.TextAppearance_AppCompat_Widget_TextView_SpinnerItem;
    public static final int TextAppearance_Compat_Notification = com.kaolafm.opensdk.R.style.TextAppearance_Compat_Notification;
    public static final int TextAppearance_Compat_Notification_Info = com.kaolafm.opensdk.R.style.TextAppearance_Compat_Notification_Info;
    public static final int TextAppearance_Compat_Notification_Line2 = com.kaolafm.opensdk.R.style.TextAppearance_Compat_Notification_Line2;
    public static final int TextAppearance_Compat_Notification_Time = com.kaolafm.opensdk.R.style.TextAppearance_Compat_Notification_Time;
    public static final int TextAppearance_Compat_Notification_Title = com.kaolafm.opensdk.R.style.TextAppearance_Compat_Notification_Title;
    public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = com.kaolafm.opensdk.R.style.TextAppearance_Widget_AppCompat_ExpandedMenu_Item;
    public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = com.kaolafm.opensdk.R.style.TextAppearance_Widget_AppCompat_Toolbar_Subtitle;
    public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = com.kaolafm.opensdk.R.style.TextAppearance_Widget_AppCompat_Toolbar_Title;
    public static final int ThemeOverlay_AppCompat = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat;
    public static final int ThemeOverlay_AppCompat_ActionBar = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_ActionBar;
    public static final int ThemeOverlay_AppCompat_Dark = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_Dark;
    public static final int ThemeOverlay_AppCompat_Dark_ActionBar = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_Dark_ActionBar;
    public static final int ThemeOverlay_AppCompat_DayNight = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_DayNight;
    public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_DayNight_ActionBar;
    public static final int ThemeOverlay_AppCompat_Dialog = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_Dialog;
    public static final int ThemeOverlay_AppCompat_Dialog_Alert = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_Dialog_Alert;
    public static final int ThemeOverlay_AppCompat_Light = com.kaolafm.opensdk.R.style.ThemeOverlay_AppCompat_Light;
    public static final int Theme_AppCompat = com.kaolafm.opensdk.R.style.Theme_AppCompat;
    public static final int Theme_AppCompat_CompactMenu = com.kaolafm.opensdk.R.style.Theme_AppCompat_CompactMenu;
    public static final int Theme_AppCompat_DayNight = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight;
    public static final int Theme_AppCompat_DayNight_DarkActionBar = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_DarkActionBar;
    public static final int Theme_AppCompat_DayNight_Dialog = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_Dialog;
    public static final int Theme_AppCompat_DayNight_DialogWhenLarge = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_DialogWhenLarge;
    public static final int Theme_AppCompat_DayNight_Dialog_Alert = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_Dialog_Alert;
    public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_Dialog_MinWidth;
    public static final int Theme_AppCompat_DayNight_NoActionBar = com.kaolafm.opensdk.R.style.Theme_AppCompat_DayNight_NoActionBar;
    public static final int Theme_AppCompat_Dialog = com.kaolafm.opensdk.R.style.Theme_AppCompat_Dialog;
    public static final int Theme_AppCompat_DialogWhenLarge = com.kaolafm.opensdk.R.style.Theme_AppCompat_DialogWhenLarge;
    public static final int Theme_AppCompat_Dialog_Alert = com.kaolafm.opensdk.R.style.Theme_AppCompat_Dialog_Alert;
    public static final int Theme_AppCompat_Dialog_MinWidth = com.kaolafm.opensdk.R.style.Theme_AppCompat_Dialog_MinWidth;
    public static final int Theme_AppCompat_Empty = com.kaolafm.opensdk.R.style.Theme_AppCompat_Empty;
    public static final int Theme_AppCompat_Light = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light;
    public static final int Theme_AppCompat_Light_DarkActionBar = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_DarkActionBar;
    public static final int Theme_AppCompat_Light_Dialog = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_Dialog;
    public static final int Theme_AppCompat_Light_DialogWhenLarge = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_DialogWhenLarge;
    public static final int Theme_AppCompat_Light_Dialog_Alert = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_Dialog_Alert;
    public static final int Theme_AppCompat_Light_Dialog_MinWidth = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_Dialog_MinWidth;
    public static final int Theme_AppCompat_Light_NoActionBar = com.kaolafm.opensdk.R.style.Theme_AppCompat_Light_NoActionBar;
    public static final int Theme_AppCompat_NoActionBar = com.kaolafm.opensdk.R.style.Theme_AppCompat_NoActionBar;
    public static final int Widget_AppCompat_ActionBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionBar;
    public static final int Widget_AppCompat_ActionBar_Solid = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionBar_Solid;
    public static final int Widget_AppCompat_ActionBar_TabBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionBar_TabBar;
    public static final int Widget_AppCompat_ActionBar_TabText = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionBar_TabText;
    public static final int Widget_AppCompat_ActionBar_TabView = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionBar_TabView;
    public static final int Widget_AppCompat_ActionButton = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionButton;
    public static final int Widget_AppCompat_ActionButton_CloseMode = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionButton_CloseMode;
    public static final int Widget_AppCompat_ActionButton_Overflow = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionButton_Overflow;
    public static final int Widget_AppCompat_ActionMode = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActionMode;
    public static final int Widget_AppCompat_ActivityChooserView = com.kaolafm.opensdk.R.style.Widget_AppCompat_ActivityChooserView;
    public static final int Widget_AppCompat_AutoCompleteTextView = com.kaolafm.opensdk.R.style.Widget_AppCompat_AutoCompleteTextView;
    public static final int Widget_AppCompat_Button = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button;
    public static final int Widget_AppCompat_ButtonBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_ButtonBar;
    public static final int Widget_AppCompat_ButtonBar_AlertDialog = com.kaolafm.opensdk.R.style.Widget_AppCompat_ButtonBar_AlertDialog;
    public static final int Widget_AppCompat_Button_Borderless = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button_Borderless;
    public static final int Widget_AppCompat_Button_Borderless_Colored = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button_Borderless_Colored;
    public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button_ButtonBar_AlertDialog;
    public static final int Widget_AppCompat_Button_Colored = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button_Colored;
    public static final int Widget_AppCompat_Button_Small = com.kaolafm.opensdk.R.style.Widget_AppCompat_Button_Small;
    public static final int Widget_AppCompat_CompoundButton_CheckBox = com.kaolafm.opensdk.R.style.Widget_AppCompat_CompoundButton_CheckBox;
    public static final int Widget_AppCompat_CompoundButton_RadioButton = com.kaolafm.opensdk.R.style.Widget_AppCompat_CompoundButton_RadioButton;
    public static final int Widget_AppCompat_CompoundButton_Switch = com.kaolafm.opensdk.R.style.Widget_AppCompat_CompoundButton_Switch;
    public static final int Widget_AppCompat_DrawerArrowToggle = com.kaolafm.opensdk.R.style.Widget_AppCompat_DrawerArrowToggle;
    public static final int Widget_AppCompat_DropDownItem_Spinner = com.kaolafm.opensdk.R.style.Widget_AppCompat_DropDownItem_Spinner;
    public static final int Widget_AppCompat_EditText = com.kaolafm.opensdk.R.style.Widget_AppCompat_EditText;
    public static final int Widget_AppCompat_ImageButton = com.kaolafm.opensdk.R.style.Widget_AppCompat_ImageButton;
    public static final int Widget_AppCompat_Light_ActionBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar;
    public static final int Widget_AppCompat_Light_ActionBar_Solid = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_Solid;
    public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_Solid_Inverse;
    public static final int Widget_AppCompat_Light_ActionBar_TabBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabBar;
    public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabBar_Inverse;
    public static final int Widget_AppCompat_Light_ActionBar_TabText = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabText;
    public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabText_Inverse;
    public static final int Widget_AppCompat_Light_ActionBar_TabView = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabView;
    public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionBar_TabView_Inverse;
    public static final int Widget_AppCompat_Light_ActionButton = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionButton;
    public static final int Widget_AppCompat_Light_ActionButton_CloseMode = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionButton_CloseMode;
    public static final int Widget_AppCompat_Light_ActionButton_Overflow = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionButton_Overflow;
    public static final int Widget_AppCompat_Light_ActionMode_Inverse = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActionMode_Inverse;
    public static final int Widget_AppCompat_Light_ActivityChooserView = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ActivityChooserView;
    public static final int Widget_AppCompat_Light_AutoCompleteTextView = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_AutoCompleteTextView;
    public static final int Widget_AppCompat_Light_DropDownItem_Spinner = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_DropDownItem_Spinner;
    public static final int Widget_AppCompat_Light_ListPopupWindow = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ListPopupWindow;
    public static final int Widget_AppCompat_Light_ListView_DropDown = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_ListView_DropDown;
    public static final int Widget_AppCompat_Light_PopupMenu = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_PopupMenu;
    public static final int Widget_AppCompat_Light_PopupMenu_Overflow = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_PopupMenu_Overflow;
    public static final int Widget_AppCompat_Light_SearchView = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_SearchView;
    public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_Light_Spinner_DropDown_ActionBar;
    public static final int Widget_AppCompat_ListMenuView = com.kaolafm.opensdk.R.style.Widget_AppCompat_ListMenuView;
    public static final int Widget_AppCompat_ListPopupWindow = com.kaolafm.opensdk.R.style.Widget_AppCompat_ListPopupWindow;
    public static final int Widget_AppCompat_ListView = com.kaolafm.opensdk.R.style.Widget_AppCompat_ListView;
    public static final int Widget_AppCompat_ListView_DropDown = com.kaolafm.opensdk.R.style.Widget_AppCompat_ListView_DropDown;
    public static final int Widget_AppCompat_ListView_Menu = com.kaolafm.opensdk.R.style.Widget_AppCompat_ListView_Menu;
    public static final int Widget_AppCompat_PopupMenu = com.kaolafm.opensdk.R.style.Widget_AppCompat_PopupMenu;
    public static final int Widget_AppCompat_PopupMenu_Overflow = com.kaolafm.opensdk.R.style.Widget_AppCompat_PopupMenu_Overflow;
    public static final int Widget_AppCompat_PopupWindow = com.kaolafm.opensdk.R.style.Widget_AppCompat_PopupWindow;
    public static final int Widget_AppCompat_ProgressBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_ProgressBar;
    public static final int Widget_AppCompat_ProgressBar_Horizontal = com.kaolafm.opensdk.R.style.Widget_AppCompat_ProgressBar_Horizontal;
    public static final int Widget_AppCompat_RatingBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_RatingBar;
    public static final int Widget_AppCompat_RatingBar_Indicator = com.kaolafm.opensdk.R.style.Widget_AppCompat_RatingBar_Indicator;
    public static final int Widget_AppCompat_RatingBar_Small = com.kaolafm.opensdk.R.style.Widget_AppCompat_RatingBar_Small;
    public static final int Widget_AppCompat_SearchView = com.kaolafm.opensdk.R.style.Widget_AppCompat_SearchView;
    public static final int Widget_AppCompat_SearchView_ActionBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_SearchView_ActionBar;
    public static final int Widget_AppCompat_SeekBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_SeekBar;
    public static final int Widget_AppCompat_SeekBar_Discrete = com.kaolafm.opensdk.R.style.Widget_AppCompat_SeekBar_Discrete;
    public static final int Widget_AppCompat_Spinner = com.kaolafm.opensdk.R.style.Widget_AppCompat_Spinner;
    public static final int Widget_AppCompat_Spinner_DropDown = com.kaolafm.opensdk.R.style.Widget_AppCompat_Spinner_DropDown;
    public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = com.kaolafm.opensdk.R.style.Widget_AppCompat_Spinner_DropDown_ActionBar;
    public static final int Widget_AppCompat_Spinner_Underlined = com.kaolafm.opensdk.R.style.Widget_AppCompat_Spinner_Underlined;
    public static final int Widget_AppCompat_TextView = com.kaolafm.opensdk.R.style.Widget_AppCompat_TextView;
    public static final int Widget_AppCompat_TextView_SpinnerItem = com.kaolafm.opensdk.R.style.Widget_AppCompat_TextView_SpinnerItem;
    public static final int Widget_AppCompat_Toolbar = com.kaolafm.opensdk.R.style.Widget_AppCompat_Toolbar;
    public static final int Widget_AppCompat_Toolbar_Button_Navigation = com.kaolafm.opensdk.R.style.Widget_AppCompat_Toolbar_Button_Navigation;
    public static final int Widget_Compat_NotificationActionContainer = com.kaolafm.opensdk.R.style.Widget_Compat_NotificationActionContainer;
    public static final int Widget_Compat_NotificationActionText = com.kaolafm.opensdk.R.style.Widget_Compat_NotificationActionText;
    }
  public static final class styleable {
    public static final int[] ActionBar = com.kaolafm.opensdk.R.styleable.ActionBar;
    public static final int ActionBar_background = com.kaolafm.opensdk.R.styleable.ActionBar_background;
    public static final int ActionBar_backgroundSplit = com.kaolafm.opensdk.R.styleable.ActionBar_backgroundSplit;
    public static final int ActionBar_backgroundStacked = com.kaolafm.opensdk.R.styleable.ActionBar_backgroundStacked;
    public static final int ActionBar_contentInsetEnd = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetEnd;
    public static final int ActionBar_contentInsetEndWithActions = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetEndWithActions;
    public static final int ActionBar_contentInsetLeft = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetLeft;
    public static final int ActionBar_contentInsetRight = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetRight;
    public static final int ActionBar_contentInsetStart = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetStart;
    public static final int ActionBar_contentInsetStartWithNavigation = com.kaolafm.opensdk.R.styleable.ActionBar_contentInsetStartWithNavigation;
    public static final int ActionBar_customNavigationLayout = com.kaolafm.opensdk.R.styleable.ActionBar_customNavigationLayout;
    public static final int ActionBar_displayOptions = com.kaolafm.opensdk.R.styleable.ActionBar_displayOptions;
    public static final int ActionBar_divider = com.kaolafm.opensdk.R.styleable.ActionBar_divider;
    public static final int ActionBar_elevation = com.kaolafm.opensdk.R.styleable.ActionBar_elevation;
    public static final int ActionBar_height = com.kaolafm.opensdk.R.styleable.ActionBar_height;
    public static final int ActionBar_hideOnContentScroll = com.kaolafm.opensdk.R.styleable.ActionBar_hideOnContentScroll;
    public static final int ActionBar_homeAsUpIndicator = com.kaolafm.opensdk.R.styleable.ActionBar_homeAsUpIndicator;
    public static final int ActionBar_homeLayout = com.kaolafm.opensdk.R.styleable.ActionBar_homeLayout;
    public static final int ActionBar_icon = com.kaolafm.opensdk.R.styleable.ActionBar_icon;
    public static final int ActionBar_indeterminateProgressStyle = com.kaolafm.opensdk.R.styleable.ActionBar_indeterminateProgressStyle;
    public static final int ActionBar_itemPadding = com.kaolafm.opensdk.R.styleable.ActionBar_itemPadding;
    public static final int ActionBar_logo = com.kaolafm.opensdk.R.styleable.ActionBar_logo;
    public static final int ActionBar_navigationMode = com.kaolafm.opensdk.R.styleable.ActionBar_navigationMode;
    public static final int ActionBar_popupTheme = com.kaolafm.opensdk.R.styleable.ActionBar_popupTheme;
    public static final int ActionBar_progressBarPadding = com.kaolafm.opensdk.R.styleable.ActionBar_progressBarPadding;
    public static final int ActionBar_progressBarStyle = com.kaolafm.opensdk.R.styleable.ActionBar_progressBarStyle;
    public static final int ActionBar_subtitle = com.kaolafm.opensdk.R.styleable.ActionBar_subtitle;
    public static final int ActionBar_subtitleTextStyle = com.kaolafm.opensdk.R.styleable.ActionBar_subtitleTextStyle;
    public static final int ActionBar_title = com.kaolafm.opensdk.R.styleable.ActionBar_title;
    public static final int ActionBar_titleTextStyle = com.kaolafm.opensdk.R.styleable.ActionBar_titleTextStyle;
    public static final int[] ActionBarLayout = com.kaolafm.opensdk.R.styleable.ActionBarLayout;
    public static final int ActionBarLayout_android_layout_gravity = com.kaolafm.opensdk.R.styleable.ActionBarLayout_android_layout_gravity;
    public static final int[] ActionMenuItemView = com.kaolafm.opensdk.R.styleable.ActionMenuItemView;
    public static final int ActionMenuItemView_android_minWidth = com.kaolafm.opensdk.R.styleable.ActionMenuItemView_android_minWidth;
    public static final int[] ActionMenuView = com.kaolafm.opensdk.R.styleable.ActionMenuView;
    public static final int[] ActionMode = com.kaolafm.opensdk.R.styleable.ActionMode;
    public static final int ActionMode_background = com.kaolafm.opensdk.R.styleable.ActionMode_background;
    public static final int ActionMode_backgroundSplit = com.kaolafm.opensdk.R.styleable.ActionMode_backgroundSplit;
    public static final int ActionMode_closeItemLayout = com.kaolafm.opensdk.R.styleable.ActionMode_closeItemLayout;
    public static final int ActionMode_height = com.kaolafm.opensdk.R.styleable.ActionMode_height;
    public static final int ActionMode_subtitleTextStyle = com.kaolafm.opensdk.R.styleable.ActionMode_subtitleTextStyle;
    public static final int ActionMode_titleTextStyle = com.kaolafm.opensdk.R.styleable.ActionMode_titleTextStyle;
    public static final int[] ActivityChooserView = com.kaolafm.opensdk.R.styleable.ActivityChooserView;
    public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = com.kaolafm.opensdk.R.styleable.ActivityChooserView_expandActivityOverflowButtonDrawable;
    public static final int ActivityChooserView_initialActivityCount = com.kaolafm.opensdk.R.styleable.ActivityChooserView_initialActivityCount;
    public static final int[] AlertDialog = com.kaolafm.opensdk.R.styleable.AlertDialog;
    public static final int AlertDialog_android_layout = com.kaolafm.opensdk.R.styleable.AlertDialog_android_layout;
    public static final int AlertDialog_buttonIconDimen = com.kaolafm.opensdk.R.styleable.AlertDialog_buttonIconDimen;
    public static final int AlertDialog_buttonPanelSideLayout = com.kaolafm.opensdk.R.styleable.AlertDialog_buttonPanelSideLayout;
    public static final int AlertDialog_listItemLayout = com.kaolafm.opensdk.R.styleable.AlertDialog_listItemLayout;
    public static final int AlertDialog_listLayout = com.kaolafm.opensdk.R.styleable.AlertDialog_listLayout;
    public static final int AlertDialog_multiChoiceItemLayout = com.kaolafm.opensdk.R.styleable.AlertDialog_multiChoiceItemLayout;
    public static final int AlertDialog_showTitle = com.kaolafm.opensdk.R.styleable.AlertDialog_showTitle;
    public static final int AlertDialog_singleChoiceItemLayout = com.kaolafm.opensdk.R.styleable.AlertDialog_singleChoiceItemLayout;
    public static final int[] AnimatedStateListDrawableCompat = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat;
    public static final int AnimatedStateListDrawableCompat_android_constantSize = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_constantSize;
    public static final int AnimatedStateListDrawableCompat_android_dither = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_dither;
    public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_enterFadeDuration;
    public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_exitFadeDuration;
    public static final int AnimatedStateListDrawableCompat_android_variablePadding = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_variablePadding;
    public static final int AnimatedStateListDrawableCompat_android_visible = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableCompat_android_visible;
    public static final int[] AnimatedStateListDrawableItem = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableItem;
    public static final int AnimatedStateListDrawableItem_android_drawable = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableItem_android_drawable;
    public static final int AnimatedStateListDrawableItem_android_id = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableItem_android_id;
    public static final int[] AnimatedStateListDrawableTransition = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableTransition;
    public static final int AnimatedStateListDrawableTransition_android_drawable = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableTransition_android_drawable;
    public static final int AnimatedStateListDrawableTransition_android_fromId = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableTransition_android_fromId;
    public static final int AnimatedStateListDrawableTransition_android_reversible = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableTransition_android_reversible;
    public static final int AnimatedStateListDrawableTransition_android_toId = com.kaolafm.opensdk.R.styleable.AnimatedStateListDrawableTransition_android_toId;
    public static final int[] AppCompatImageView = com.kaolafm.opensdk.R.styleable.AppCompatImageView;
    public static final int AppCompatImageView_android_src = com.kaolafm.opensdk.R.styleable.AppCompatImageView_android_src;
    public static final int AppCompatImageView_srcCompat = com.kaolafm.opensdk.R.styleable.AppCompatImageView_srcCompat;
    public static final int AppCompatImageView_tint = com.kaolafm.opensdk.R.styleable.AppCompatImageView_tint;
    public static final int AppCompatImageView_tintMode = com.kaolafm.opensdk.R.styleable.AppCompatImageView_tintMode;
    public static final int[] AppCompatSeekBar = com.kaolafm.opensdk.R.styleable.AppCompatSeekBar;
    public static final int AppCompatSeekBar_android_thumb = com.kaolafm.opensdk.R.styleable.AppCompatSeekBar_android_thumb;
    public static final int AppCompatSeekBar_tickMark = com.kaolafm.opensdk.R.styleable.AppCompatSeekBar_tickMark;
    public static final int AppCompatSeekBar_tickMarkTint = com.kaolafm.opensdk.R.styleable.AppCompatSeekBar_tickMarkTint;
    public static final int AppCompatSeekBar_tickMarkTintMode = com.kaolafm.opensdk.R.styleable.AppCompatSeekBar_tickMarkTintMode;
    public static final int[] AppCompatTextHelper = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper;
    public static final int AppCompatTextHelper_android_drawableBottom = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableBottom;
    public static final int AppCompatTextHelper_android_drawableEnd = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableEnd;
    public static final int AppCompatTextHelper_android_drawableLeft = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableLeft;
    public static final int AppCompatTextHelper_android_drawableRight = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableRight;
    public static final int AppCompatTextHelper_android_drawableStart = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableStart;
    public static final int AppCompatTextHelper_android_drawableTop = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_drawableTop;
    public static final int AppCompatTextHelper_android_textAppearance = com.kaolafm.opensdk.R.styleable.AppCompatTextHelper_android_textAppearance;
    public static final int[] AppCompatTextView = com.kaolafm.opensdk.R.styleable.AppCompatTextView;
    public static final int AppCompatTextView_android_textAppearance = com.kaolafm.opensdk.R.styleable.AppCompatTextView_android_textAppearance;
    public static final int AppCompatTextView_autoSizeMaxTextSize = com.kaolafm.opensdk.R.styleable.AppCompatTextView_autoSizeMaxTextSize;
    public static final int AppCompatTextView_autoSizeMinTextSize = com.kaolafm.opensdk.R.styleable.AppCompatTextView_autoSizeMinTextSize;
    public static final int AppCompatTextView_autoSizePresetSizes = com.kaolafm.opensdk.R.styleable.AppCompatTextView_autoSizePresetSizes;
    public static final int AppCompatTextView_autoSizeStepGranularity = com.kaolafm.opensdk.R.styleable.AppCompatTextView_autoSizeStepGranularity;
    public static final int AppCompatTextView_autoSizeTextType = com.kaolafm.opensdk.R.styleable.AppCompatTextView_autoSizeTextType;
    public static final int AppCompatTextView_drawableBottomCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableBottomCompat;
    public static final int AppCompatTextView_drawableEndCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableEndCompat;
    public static final int AppCompatTextView_drawableLeftCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableLeftCompat;
    public static final int AppCompatTextView_drawableRightCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableRightCompat;
    public static final int AppCompatTextView_drawableStartCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableStartCompat;
    public static final int AppCompatTextView_drawableTint = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableTint;
    public static final int AppCompatTextView_drawableTintMode = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableTintMode;
    public static final int AppCompatTextView_drawableTopCompat = com.kaolafm.opensdk.R.styleable.AppCompatTextView_drawableTopCompat;
    public static final int AppCompatTextView_firstBaselineToTopHeight = com.kaolafm.opensdk.R.styleable.AppCompatTextView_firstBaselineToTopHeight;
    public static final int AppCompatTextView_fontFamily = com.kaolafm.opensdk.R.styleable.AppCompatTextView_fontFamily;
    public static final int AppCompatTextView_fontVariationSettings = com.kaolafm.opensdk.R.styleable.AppCompatTextView_fontVariationSettings;
    public static final int AppCompatTextView_lastBaselineToBottomHeight = com.kaolafm.opensdk.R.styleable.AppCompatTextView_lastBaselineToBottomHeight;
    public static final int AppCompatTextView_lineHeight = com.kaolafm.opensdk.R.styleable.AppCompatTextView_lineHeight;
    public static final int AppCompatTextView_textAllCaps = com.kaolafm.opensdk.R.styleable.AppCompatTextView_textAllCaps;
    public static final int AppCompatTextView_textLocale = com.kaolafm.opensdk.R.styleable.AppCompatTextView_textLocale;
    public static final int[] AppCompatTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme;
    public static final int AppCompatTheme_actionBarDivider = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarDivider;
    public static final int AppCompatTheme_actionBarItemBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarItemBackground;
    public static final int AppCompatTheme_actionBarPopupTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarPopupTheme;
    public static final int AppCompatTheme_actionBarSize = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarSize;
    public static final int AppCompatTheme_actionBarSplitStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarSplitStyle;
    public static final int AppCompatTheme_actionBarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarStyle;
    public static final int AppCompatTheme_actionBarTabBarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarTabBarStyle;
    public static final int AppCompatTheme_actionBarTabStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarTabStyle;
    public static final int AppCompatTheme_actionBarTabTextStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarTabTextStyle;
    public static final int AppCompatTheme_actionBarTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarTheme;
    public static final int AppCompatTheme_actionBarWidgetTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionBarWidgetTheme;
    public static final int AppCompatTheme_actionButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionButtonStyle;
    public static final int AppCompatTheme_actionDropDownStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionDropDownStyle;
    public static final int AppCompatTheme_actionMenuTextAppearance = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionMenuTextAppearance;
    public static final int AppCompatTheme_actionMenuTextColor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionMenuTextColor;
    public static final int AppCompatTheme_actionModeBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeBackground;
    public static final int AppCompatTheme_actionModeCloseButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeCloseButtonStyle;
    public static final int AppCompatTheme_actionModeCloseDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeCloseDrawable;
    public static final int AppCompatTheme_actionModeCopyDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeCopyDrawable;
    public static final int AppCompatTheme_actionModeCutDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeCutDrawable;
    public static final int AppCompatTheme_actionModeFindDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeFindDrawable;
    public static final int AppCompatTheme_actionModePasteDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModePasteDrawable;
    public static final int AppCompatTheme_actionModePopupWindowStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModePopupWindowStyle;
    public static final int AppCompatTheme_actionModeSelectAllDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeSelectAllDrawable;
    public static final int AppCompatTheme_actionModeShareDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeShareDrawable;
    public static final int AppCompatTheme_actionModeSplitBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeSplitBackground;
    public static final int AppCompatTheme_actionModeStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeStyle;
    public static final int AppCompatTheme_actionModeWebSearchDrawable = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionModeWebSearchDrawable;
    public static final int AppCompatTheme_actionOverflowButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionOverflowButtonStyle;
    public static final int AppCompatTheme_actionOverflowMenuStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_actionOverflowMenuStyle;
    public static final int AppCompatTheme_activityChooserViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_activityChooserViewStyle;
    public static final int AppCompatTheme_alertDialogButtonGroupStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_alertDialogButtonGroupStyle;
    public static final int AppCompatTheme_alertDialogCenterButtons = com.kaolafm.opensdk.R.styleable.AppCompatTheme_alertDialogCenterButtons;
    public static final int AppCompatTheme_alertDialogStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_alertDialogStyle;
    public static final int AppCompatTheme_alertDialogTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_alertDialogTheme;
    public static final int AppCompatTheme_android_windowAnimationStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_android_windowAnimationStyle;
    public static final int AppCompatTheme_android_windowIsFloating = com.kaolafm.opensdk.R.styleable.AppCompatTheme_android_windowIsFloating;
    public static final int AppCompatTheme_autoCompleteTextViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_autoCompleteTextViewStyle;
    public static final int AppCompatTheme_borderlessButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_borderlessButtonStyle;
    public static final int AppCompatTheme_buttonBarButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonBarButtonStyle;
    public static final int AppCompatTheme_buttonBarNegativeButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonBarNegativeButtonStyle;
    public static final int AppCompatTheme_buttonBarNeutralButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonBarNeutralButtonStyle;
    public static final int AppCompatTheme_buttonBarPositiveButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonBarPositiveButtonStyle;
    public static final int AppCompatTheme_buttonBarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonBarStyle;
    public static final int AppCompatTheme_buttonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonStyle;
    public static final int AppCompatTheme_buttonStyleSmall = com.kaolafm.opensdk.R.styleable.AppCompatTheme_buttonStyleSmall;
    public static final int AppCompatTheme_checkboxStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_checkboxStyle;
    public static final int AppCompatTheme_checkedTextViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_checkedTextViewStyle;
    public static final int AppCompatTheme_colorAccent = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorAccent;
    public static final int AppCompatTheme_colorBackgroundFloating = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorBackgroundFloating;
    public static final int AppCompatTheme_colorButtonNormal = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorButtonNormal;
    public static final int AppCompatTheme_colorControlActivated = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorControlActivated;
    public static final int AppCompatTheme_colorControlHighlight = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorControlHighlight;
    public static final int AppCompatTheme_colorControlNormal = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorControlNormal;
    public static final int AppCompatTheme_colorError = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorError;
    public static final int AppCompatTheme_colorPrimary = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorPrimary;
    public static final int AppCompatTheme_colorPrimaryDark = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorPrimaryDark;
    public static final int AppCompatTheme_colorSwitchThumbNormal = com.kaolafm.opensdk.R.styleable.AppCompatTheme_colorSwitchThumbNormal;
    public static final int AppCompatTheme_controlBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_controlBackground;
    public static final int AppCompatTheme_dialogCornerRadius = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dialogCornerRadius;
    public static final int AppCompatTheme_dialogPreferredPadding = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dialogPreferredPadding;
    public static final int AppCompatTheme_dialogTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dialogTheme;
    public static final int AppCompatTheme_dividerHorizontal = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dividerHorizontal;
    public static final int AppCompatTheme_dividerVertical = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dividerVertical;
    public static final int AppCompatTheme_dropDownListViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dropDownListViewStyle;
    public static final int AppCompatTheme_dropdownListPreferredItemHeight = com.kaolafm.opensdk.R.styleable.AppCompatTheme_dropdownListPreferredItemHeight;
    public static final int AppCompatTheme_editTextBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_editTextBackground;
    public static final int AppCompatTheme_editTextColor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_editTextColor;
    public static final int AppCompatTheme_editTextStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_editTextStyle;
    public static final int AppCompatTheme_homeAsUpIndicator = com.kaolafm.opensdk.R.styleable.AppCompatTheme_homeAsUpIndicator;
    public static final int AppCompatTheme_imageButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_imageButtonStyle;
    public static final int AppCompatTheme_listChoiceBackgroundIndicator = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listChoiceBackgroundIndicator;
    public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listChoiceIndicatorMultipleAnimated;
    public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listChoiceIndicatorSingleAnimated;
    public static final int AppCompatTheme_listDividerAlertDialog = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listDividerAlertDialog;
    public static final int AppCompatTheme_listMenuViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listMenuViewStyle;
    public static final int AppCompatTheme_listPopupWindowStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPopupWindowStyle;
    public static final int AppCompatTheme_listPreferredItemHeight = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemHeight;
    public static final int AppCompatTheme_listPreferredItemHeightLarge = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemHeightLarge;
    public static final int AppCompatTheme_listPreferredItemHeightSmall = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemHeightSmall;
    public static final int AppCompatTheme_listPreferredItemPaddingEnd = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemPaddingEnd;
    public static final int AppCompatTheme_listPreferredItemPaddingLeft = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemPaddingLeft;
    public static final int AppCompatTheme_listPreferredItemPaddingRight = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemPaddingRight;
    public static final int AppCompatTheme_listPreferredItemPaddingStart = com.kaolafm.opensdk.R.styleable.AppCompatTheme_listPreferredItemPaddingStart;
    public static final int AppCompatTheme_panelBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_panelBackground;
    public static final int AppCompatTheme_panelMenuListTheme = com.kaolafm.opensdk.R.styleable.AppCompatTheme_panelMenuListTheme;
    public static final int AppCompatTheme_panelMenuListWidth = com.kaolafm.opensdk.R.styleable.AppCompatTheme_panelMenuListWidth;
    public static final int AppCompatTheme_popupMenuStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_popupMenuStyle;
    public static final int AppCompatTheme_popupWindowStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_popupWindowStyle;
    public static final int AppCompatTheme_radioButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_radioButtonStyle;
    public static final int AppCompatTheme_ratingBarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_ratingBarStyle;
    public static final int AppCompatTheme_ratingBarStyleIndicator = com.kaolafm.opensdk.R.styleable.AppCompatTheme_ratingBarStyleIndicator;
    public static final int AppCompatTheme_ratingBarStyleSmall = com.kaolafm.opensdk.R.styleable.AppCompatTheme_ratingBarStyleSmall;
    public static final int AppCompatTheme_searchViewStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_searchViewStyle;
    public static final int AppCompatTheme_seekBarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_seekBarStyle;
    public static final int AppCompatTheme_selectableItemBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_selectableItemBackground;
    public static final int AppCompatTheme_selectableItemBackgroundBorderless = com.kaolafm.opensdk.R.styleable.AppCompatTheme_selectableItemBackgroundBorderless;
    public static final int AppCompatTheme_spinnerDropDownItemStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_spinnerDropDownItemStyle;
    public static final int AppCompatTheme_spinnerStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_spinnerStyle;
    public static final int AppCompatTheme_switchStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_switchStyle;
    public static final int AppCompatTheme_textAppearanceLargePopupMenu = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceLargePopupMenu;
    public static final int AppCompatTheme_textAppearanceListItem = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceListItem;
    public static final int AppCompatTheme_textAppearanceListItemSecondary = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceListItemSecondary;
    public static final int AppCompatTheme_textAppearanceListItemSmall = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceListItemSmall;
    public static final int AppCompatTheme_textAppearancePopupMenuHeader = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearancePopupMenuHeader;
    public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceSearchResultSubtitle;
    public static final int AppCompatTheme_textAppearanceSearchResultTitle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceSearchResultTitle;
    public static final int AppCompatTheme_textAppearanceSmallPopupMenu = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textAppearanceSmallPopupMenu;
    public static final int AppCompatTheme_textColorAlertDialogListItem = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textColorAlertDialogListItem;
    public static final int AppCompatTheme_textColorSearchUrl = com.kaolafm.opensdk.R.styleable.AppCompatTheme_textColorSearchUrl;
    public static final int AppCompatTheme_toolbarNavigationButtonStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_toolbarNavigationButtonStyle;
    public static final int AppCompatTheme_toolbarStyle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_toolbarStyle;
    public static final int AppCompatTheme_tooltipForegroundColor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_tooltipForegroundColor;
    public static final int AppCompatTheme_tooltipFrameBackground = com.kaolafm.opensdk.R.styleable.AppCompatTheme_tooltipFrameBackground;
    public static final int AppCompatTheme_viewInflaterClass = com.kaolafm.opensdk.R.styleable.AppCompatTheme_viewInflaterClass;
    public static final int AppCompatTheme_windowActionBar = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowActionBar;
    public static final int AppCompatTheme_windowActionBarOverlay = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowActionBarOverlay;
    public static final int AppCompatTheme_windowActionModeOverlay = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowActionModeOverlay;
    public static final int AppCompatTheme_windowFixedHeightMajor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowFixedHeightMajor;
    public static final int AppCompatTheme_windowFixedHeightMinor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowFixedHeightMinor;
    public static final int AppCompatTheme_windowFixedWidthMajor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowFixedWidthMajor;
    public static final int AppCompatTheme_windowFixedWidthMinor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowFixedWidthMinor;
    public static final int AppCompatTheme_windowMinWidthMajor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowMinWidthMajor;
    public static final int AppCompatTheme_windowMinWidthMinor = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowMinWidthMinor;
    public static final int AppCompatTheme_windowNoTitle = com.kaolafm.opensdk.R.styleable.AppCompatTheme_windowNoTitle;
    public static final int[] ButtonBarLayout = com.kaolafm.opensdk.R.styleable.ButtonBarLayout;
    public static final int ButtonBarLayout_allowStacking = com.kaolafm.opensdk.R.styleable.ButtonBarLayout_allowStacking;
    public static final int[] Capability = com.kaolafm.opensdk.R.styleable.Capability;
    public static final int Capability_queryPatterns = com.kaolafm.opensdk.R.styleable.Capability_queryPatterns;
    public static final int Capability_shortcutMatchRequired = com.kaolafm.opensdk.R.styleable.Capability_shortcutMatchRequired;
    public static final int[] ColorStateListItem = com.kaolafm.opensdk.R.styleable.ColorStateListItem;
    public static final int ColorStateListItem_alpha = com.kaolafm.opensdk.R.styleable.ColorStateListItem_alpha;
    public static final int ColorStateListItem_android_alpha = com.kaolafm.opensdk.R.styleable.ColorStateListItem_android_alpha;
    public static final int ColorStateListItem_android_color = com.kaolafm.opensdk.R.styleable.ColorStateListItem_android_color;
    public static final int ColorStateListItem_android_lStar = com.kaolafm.opensdk.R.styleable.ColorStateListItem_android_lStar;
    public static final int ColorStateListItem_lStar = com.kaolafm.opensdk.R.styleable.ColorStateListItem_lStar;
    public static final int[] CompoundButton = com.kaolafm.opensdk.R.styleable.CompoundButton;
    public static final int CompoundButton_android_button = com.kaolafm.opensdk.R.styleable.CompoundButton_android_button;
    public static final int CompoundButton_buttonCompat = com.kaolafm.opensdk.R.styleable.CompoundButton_buttonCompat;
    public static final int CompoundButton_buttonTint = com.kaolafm.opensdk.R.styleable.CompoundButton_buttonTint;
    public static final int CompoundButton_buttonTintMode = com.kaolafm.opensdk.R.styleable.CompoundButton_buttonTintMode;
    public static final int[] DrawerArrowToggle = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle;
    public static final int DrawerArrowToggle_arrowHeadLength = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_arrowHeadLength;
    public static final int DrawerArrowToggle_arrowShaftLength = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_arrowShaftLength;
    public static final int DrawerArrowToggle_barLength = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_barLength;
    public static final int DrawerArrowToggle_color = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_color;
    public static final int DrawerArrowToggle_drawableSize = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_drawableSize;
    public static final int DrawerArrowToggle_gapBetweenBars = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_gapBetweenBars;
    public static final int DrawerArrowToggle_spinBars = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_spinBars;
    public static final int DrawerArrowToggle_thickness = com.kaolafm.opensdk.R.styleable.DrawerArrowToggle_thickness;
    public static final int[] FontFamily = com.kaolafm.opensdk.R.styleable.FontFamily;
    public static final int FontFamily_fontProviderAuthority = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderAuthority;
    public static final int FontFamily_fontProviderCerts = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderCerts;
    public static final int FontFamily_fontProviderFetchStrategy = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderFetchStrategy;
    public static final int FontFamily_fontProviderFetchTimeout = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderFetchTimeout;
    public static final int FontFamily_fontProviderPackage = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderPackage;
    public static final int FontFamily_fontProviderQuery = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderQuery;
    public static final int FontFamily_fontProviderSystemFontFamily = com.kaolafm.opensdk.R.styleable.FontFamily_fontProviderSystemFontFamily;
    public static final int[] FontFamilyFont = com.kaolafm.opensdk.R.styleable.FontFamilyFont;
    public static final int FontFamilyFont_android_font = com.kaolafm.opensdk.R.styleable.FontFamilyFont_android_font;
    public static final int FontFamilyFont_android_fontStyle = com.kaolafm.opensdk.R.styleable.FontFamilyFont_android_fontStyle;
    public static final int FontFamilyFont_android_fontVariationSettings = com.kaolafm.opensdk.R.styleable.FontFamilyFont_android_fontVariationSettings;
    public static final int FontFamilyFont_android_fontWeight = com.kaolafm.opensdk.R.styleable.FontFamilyFont_android_fontWeight;
    public static final int FontFamilyFont_android_ttcIndex = com.kaolafm.opensdk.R.styleable.FontFamilyFont_android_ttcIndex;
    public static final int FontFamilyFont_font = com.kaolafm.opensdk.R.styleable.FontFamilyFont_font;
    public static final int FontFamilyFont_fontStyle = com.kaolafm.opensdk.R.styleable.FontFamilyFont_fontStyle;
    public static final int FontFamilyFont_fontVariationSettings = com.kaolafm.opensdk.R.styleable.FontFamilyFont_fontVariationSettings;
    public static final int FontFamilyFont_fontWeight = com.kaolafm.opensdk.R.styleable.FontFamilyFont_fontWeight;
    public static final int FontFamilyFont_ttcIndex = com.kaolafm.opensdk.R.styleable.FontFamilyFont_ttcIndex;
    public static final int[] Fragment = com.kaolafm.opensdk.R.styleable.Fragment;
    public static final int Fragment_android_id = com.kaolafm.opensdk.R.styleable.Fragment_android_id;
    public static final int Fragment_android_name = com.kaolafm.opensdk.R.styleable.Fragment_android_name;
    public static final int Fragment_android_tag = com.kaolafm.opensdk.R.styleable.Fragment_android_tag;
    public static final int[] FragmentContainerView = com.kaolafm.opensdk.R.styleable.FragmentContainerView;
    public static final int FragmentContainerView_android_name = com.kaolafm.opensdk.R.styleable.FragmentContainerView_android_name;
    public static final int FragmentContainerView_android_tag = com.kaolafm.opensdk.R.styleable.FragmentContainerView_android_tag;
    public static final int[] GradientColor = com.kaolafm.opensdk.R.styleable.GradientColor;
    public static final int GradientColor_android_centerColor = com.kaolafm.opensdk.R.styleable.GradientColor_android_centerColor;
    public static final int GradientColor_android_centerX = com.kaolafm.opensdk.R.styleable.GradientColor_android_centerX;
    public static final int GradientColor_android_centerY = com.kaolafm.opensdk.R.styleable.GradientColor_android_centerY;
    public static final int GradientColor_android_endColor = com.kaolafm.opensdk.R.styleable.GradientColor_android_endColor;
    public static final int GradientColor_android_endX = com.kaolafm.opensdk.R.styleable.GradientColor_android_endX;
    public static final int GradientColor_android_endY = com.kaolafm.opensdk.R.styleable.GradientColor_android_endY;
    public static final int GradientColor_android_gradientRadius = com.kaolafm.opensdk.R.styleable.GradientColor_android_gradientRadius;
    public static final int GradientColor_android_startColor = com.kaolafm.opensdk.R.styleable.GradientColor_android_startColor;
    public static final int GradientColor_android_startX = com.kaolafm.opensdk.R.styleable.GradientColor_android_startX;
    public static final int GradientColor_android_startY = com.kaolafm.opensdk.R.styleable.GradientColor_android_startY;
    public static final int GradientColor_android_tileMode = com.kaolafm.opensdk.R.styleable.GradientColor_android_tileMode;
    public static final int GradientColor_android_type = com.kaolafm.opensdk.R.styleable.GradientColor_android_type;
    public static final int[] GradientColorItem = com.kaolafm.opensdk.R.styleable.GradientColorItem;
    public static final int GradientColorItem_android_color = com.kaolafm.opensdk.R.styleable.GradientColorItem_android_color;
    public static final int GradientColorItem_android_offset = com.kaolafm.opensdk.R.styleable.GradientColorItem_android_offset;
    public static final int[] LinearLayoutCompat = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat;
    public static final int LinearLayoutCompat_android_baselineAligned = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_android_baselineAligned;
    public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_android_baselineAlignedChildIndex;
    public static final int LinearLayoutCompat_android_gravity = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_android_gravity;
    public static final int LinearLayoutCompat_android_orientation = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_android_orientation;
    public static final int LinearLayoutCompat_android_weightSum = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_android_weightSum;
    public static final int LinearLayoutCompat_divider = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_divider;
    public static final int LinearLayoutCompat_dividerPadding = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_dividerPadding;
    public static final int LinearLayoutCompat_measureWithLargestChild = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_measureWithLargestChild;
    public static final int LinearLayoutCompat_showDividers = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_showDividers;
    public static final int[] LinearLayoutCompat_Layout = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_Layout;
    public static final int LinearLayoutCompat_Layout_android_layout_gravity = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_Layout_android_layout_gravity;
    public static final int LinearLayoutCompat_Layout_android_layout_height = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_Layout_android_layout_height;
    public static final int LinearLayoutCompat_Layout_android_layout_weight = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_Layout_android_layout_weight;
    public static final int LinearLayoutCompat_Layout_android_layout_width = com.kaolafm.opensdk.R.styleable.LinearLayoutCompat_Layout_android_layout_width;
    public static final int[] ListPopupWindow = com.kaolafm.opensdk.R.styleable.ListPopupWindow;
    public static final int ListPopupWindow_android_dropDownHorizontalOffset = com.kaolafm.opensdk.R.styleable.ListPopupWindow_android_dropDownHorizontalOffset;
    public static final int ListPopupWindow_android_dropDownVerticalOffset = com.kaolafm.opensdk.R.styleable.ListPopupWindow_android_dropDownVerticalOffset;
    public static final int[] MenuGroup = com.kaolafm.opensdk.R.styleable.MenuGroup;
    public static final int MenuGroup_android_checkableBehavior = com.kaolafm.opensdk.R.styleable.MenuGroup_android_checkableBehavior;
    public static final int MenuGroup_android_enabled = com.kaolafm.opensdk.R.styleable.MenuGroup_android_enabled;
    public static final int MenuGroup_android_id = com.kaolafm.opensdk.R.styleable.MenuGroup_android_id;
    public static final int MenuGroup_android_menuCategory = com.kaolafm.opensdk.R.styleable.MenuGroup_android_menuCategory;
    public static final int MenuGroup_android_orderInCategory = com.kaolafm.opensdk.R.styleable.MenuGroup_android_orderInCategory;
    public static final int MenuGroup_android_visible = com.kaolafm.opensdk.R.styleable.MenuGroup_android_visible;
    public static final int[] MenuItem = com.kaolafm.opensdk.R.styleable.MenuItem;
    public static final int MenuItem_actionLayout = com.kaolafm.opensdk.R.styleable.MenuItem_actionLayout;
    public static final int MenuItem_actionProviderClass = com.kaolafm.opensdk.R.styleable.MenuItem_actionProviderClass;
    public static final int MenuItem_actionViewClass = com.kaolafm.opensdk.R.styleable.MenuItem_actionViewClass;
    public static final int MenuItem_alphabeticModifiers = com.kaolafm.opensdk.R.styleable.MenuItem_alphabeticModifiers;
    public static final int MenuItem_android_alphabeticShortcut = com.kaolafm.opensdk.R.styleable.MenuItem_android_alphabeticShortcut;
    public static final int MenuItem_android_checkable = com.kaolafm.opensdk.R.styleable.MenuItem_android_checkable;
    public static final int MenuItem_android_checked = com.kaolafm.opensdk.R.styleable.MenuItem_android_checked;
    public static final int MenuItem_android_enabled = com.kaolafm.opensdk.R.styleable.MenuItem_android_enabled;
    public static final int MenuItem_android_icon = com.kaolafm.opensdk.R.styleable.MenuItem_android_icon;
    public static final int MenuItem_android_id = com.kaolafm.opensdk.R.styleable.MenuItem_android_id;
    public static final int MenuItem_android_menuCategory = com.kaolafm.opensdk.R.styleable.MenuItem_android_menuCategory;
    public static final int MenuItem_android_numericShortcut = com.kaolafm.opensdk.R.styleable.MenuItem_android_numericShortcut;
    public static final int MenuItem_android_onClick = com.kaolafm.opensdk.R.styleable.MenuItem_android_onClick;
    public static final int MenuItem_android_orderInCategory = com.kaolafm.opensdk.R.styleable.MenuItem_android_orderInCategory;
    public static final int MenuItem_android_title = com.kaolafm.opensdk.R.styleable.MenuItem_android_title;
    public static final int MenuItem_android_titleCondensed = com.kaolafm.opensdk.R.styleable.MenuItem_android_titleCondensed;
    public static final int MenuItem_android_visible = com.kaolafm.opensdk.R.styleable.MenuItem_android_visible;
    public static final int MenuItem_contentDescription = com.kaolafm.opensdk.R.styleable.MenuItem_contentDescription;
    public static final int MenuItem_iconTint = com.kaolafm.opensdk.R.styleable.MenuItem_iconTint;
    public static final int MenuItem_iconTintMode = com.kaolafm.opensdk.R.styleable.MenuItem_iconTintMode;
    public static final int MenuItem_numericModifiers = com.kaolafm.opensdk.R.styleable.MenuItem_numericModifiers;
    public static final int MenuItem_showAsAction = com.kaolafm.opensdk.R.styleable.MenuItem_showAsAction;
    public static final int MenuItem_tooltipText = com.kaolafm.opensdk.R.styleable.MenuItem_tooltipText;
    public static final int[] MenuView = com.kaolafm.opensdk.R.styleable.MenuView;
    public static final int MenuView_android_headerBackground = com.kaolafm.opensdk.R.styleable.MenuView_android_headerBackground;
    public static final int MenuView_android_horizontalDivider = com.kaolafm.opensdk.R.styleable.MenuView_android_horizontalDivider;
    public static final int MenuView_android_itemBackground = com.kaolafm.opensdk.R.styleable.MenuView_android_itemBackground;
    public static final int MenuView_android_itemIconDisabledAlpha = com.kaolafm.opensdk.R.styleable.MenuView_android_itemIconDisabledAlpha;
    public static final int MenuView_android_itemTextAppearance = com.kaolafm.opensdk.R.styleable.MenuView_android_itemTextAppearance;
    public static final int MenuView_android_verticalDivider = com.kaolafm.opensdk.R.styleable.MenuView_android_verticalDivider;
    public static final int MenuView_android_windowAnimationStyle = com.kaolafm.opensdk.R.styleable.MenuView_android_windowAnimationStyle;
    public static final int MenuView_preserveIconSpacing = com.kaolafm.opensdk.R.styleable.MenuView_preserveIconSpacing;
    public static final int MenuView_subMenuArrow = com.kaolafm.opensdk.R.styleable.MenuView_subMenuArrow;
    public static final int[] PopupWindow = com.kaolafm.opensdk.R.styleable.PopupWindow;
    public static final int PopupWindow_android_popupAnimationStyle = com.kaolafm.opensdk.R.styleable.PopupWindow_android_popupAnimationStyle;
    public static final int PopupWindow_android_popupBackground = com.kaolafm.opensdk.R.styleable.PopupWindow_android_popupBackground;
    public static final int PopupWindow_overlapAnchor = com.kaolafm.opensdk.R.styleable.PopupWindow_overlapAnchor;
    public static final int[] PopupWindowBackgroundState = com.kaolafm.opensdk.R.styleable.PopupWindowBackgroundState;
    public static final int PopupWindowBackgroundState_state_above_anchor = com.kaolafm.opensdk.R.styleable.PopupWindowBackgroundState_state_above_anchor;
    public static final int[] RecycleListView = com.kaolafm.opensdk.R.styleable.RecycleListView;
    public static final int RecycleListView_paddingBottomNoButtons = com.kaolafm.opensdk.R.styleable.RecycleListView_paddingBottomNoButtons;
    public static final int RecycleListView_paddingTopNoTitle = com.kaolafm.opensdk.R.styleable.RecycleListView_paddingTopNoTitle;
    public static final int[] SearchView = com.kaolafm.opensdk.R.styleable.SearchView;
    public static final int SearchView_android_focusable = com.kaolafm.opensdk.R.styleable.SearchView_android_focusable;
    public static final int SearchView_android_imeOptions = com.kaolafm.opensdk.R.styleable.SearchView_android_imeOptions;
    public static final int SearchView_android_inputType = com.kaolafm.opensdk.R.styleable.SearchView_android_inputType;
    public static final int SearchView_android_maxWidth = com.kaolafm.opensdk.R.styleable.SearchView_android_maxWidth;
    public static final int SearchView_closeIcon = com.kaolafm.opensdk.R.styleable.SearchView_closeIcon;
    public static final int SearchView_commitIcon = com.kaolafm.opensdk.R.styleable.SearchView_commitIcon;
    public static final int SearchView_defaultQueryHint = com.kaolafm.opensdk.R.styleable.SearchView_defaultQueryHint;
    public static final int SearchView_goIcon = com.kaolafm.opensdk.R.styleable.SearchView_goIcon;
    public static final int SearchView_iconifiedByDefault = com.kaolafm.opensdk.R.styleable.SearchView_iconifiedByDefault;
    public static final int SearchView_layout = com.kaolafm.opensdk.R.styleable.SearchView_layout;
    public static final int SearchView_queryBackground = com.kaolafm.opensdk.R.styleable.SearchView_queryBackground;
    public static final int SearchView_queryHint = com.kaolafm.opensdk.R.styleable.SearchView_queryHint;
    public static final int SearchView_searchHintIcon = com.kaolafm.opensdk.R.styleable.SearchView_searchHintIcon;
    public static final int SearchView_searchIcon = com.kaolafm.opensdk.R.styleable.SearchView_searchIcon;
    public static final int SearchView_submitBackground = com.kaolafm.opensdk.R.styleable.SearchView_submitBackground;
    public static final int SearchView_suggestionRowLayout = com.kaolafm.opensdk.R.styleable.SearchView_suggestionRowLayout;
    public static final int SearchView_voiceIcon = com.kaolafm.opensdk.R.styleable.SearchView_voiceIcon;
    public static final int[] Spinner = com.kaolafm.opensdk.R.styleable.Spinner;
    public static final int Spinner_android_dropDownWidth = com.kaolafm.opensdk.R.styleable.Spinner_android_dropDownWidth;
    public static final int Spinner_android_entries = com.kaolafm.opensdk.R.styleable.Spinner_android_entries;
    public static final int Spinner_android_popupBackground = com.kaolafm.opensdk.R.styleable.Spinner_android_popupBackground;
    public static final int Spinner_android_prompt = com.kaolafm.opensdk.R.styleable.Spinner_android_prompt;
    public static final int Spinner_popupTheme = com.kaolafm.opensdk.R.styleable.Spinner_popupTheme;
    public static final int[] StateListDrawable = com.kaolafm.opensdk.R.styleable.StateListDrawable;
    public static final int StateListDrawable_android_constantSize = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_constantSize;
    public static final int StateListDrawable_android_dither = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_dither;
    public static final int StateListDrawable_android_enterFadeDuration = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_enterFadeDuration;
    public static final int StateListDrawable_android_exitFadeDuration = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_exitFadeDuration;
    public static final int StateListDrawable_android_variablePadding = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_variablePadding;
    public static final int StateListDrawable_android_visible = com.kaolafm.opensdk.R.styleable.StateListDrawable_android_visible;
    public static final int[] StateListDrawableItem = com.kaolafm.opensdk.R.styleable.StateListDrawableItem;
    public static final int StateListDrawableItem_android_drawable = com.kaolafm.opensdk.R.styleable.StateListDrawableItem_android_drawable;
    public static final int[] SwitchCompat = com.kaolafm.opensdk.R.styleable.SwitchCompat;
    public static final int SwitchCompat_android_textOff = com.kaolafm.opensdk.R.styleable.SwitchCompat_android_textOff;
    public static final int SwitchCompat_android_textOn = com.kaolafm.opensdk.R.styleable.SwitchCompat_android_textOn;
    public static final int SwitchCompat_android_thumb = com.kaolafm.opensdk.R.styleable.SwitchCompat_android_thumb;
    public static final int SwitchCompat_showText = com.kaolafm.opensdk.R.styleable.SwitchCompat_showText;
    public static final int SwitchCompat_splitTrack = com.kaolafm.opensdk.R.styleable.SwitchCompat_splitTrack;
    public static final int SwitchCompat_switchMinWidth = com.kaolafm.opensdk.R.styleable.SwitchCompat_switchMinWidth;
    public static final int SwitchCompat_switchPadding = com.kaolafm.opensdk.R.styleable.SwitchCompat_switchPadding;
    public static final int SwitchCompat_switchTextAppearance = com.kaolafm.opensdk.R.styleable.SwitchCompat_switchTextAppearance;
    public static final int SwitchCompat_thumbTextPadding = com.kaolafm.opensdk.R.styleable.SwitchCompat_thumbTextPadding;
    public static final int SwitchCompat_thumbTint = com.kaolafm.opensdk.R.styleable.SwitchCompat_thumbTint;
    public static final int SwitchCompat_thumbTintMode = com.kaolafm.opensdk.R.styleable.SwitchCompat_thumbTintMode;
    public static final int SwitchCompat_track = com.kaolafm.opensdk.R.styleable.SwitchCompat_track;
    public static final int SwitchCompat_trackTint = com.kaolafm.opensdk.R.styleable.SwitchCompat_trackTint;
    public static final int SwitchCompat_trackTintMode = com.kaolafm.opensdk.R.styleable.SwitchCompat_trackTintMode;
    public static final int[] TextAppearance = com.kaolafm.opensdk.R.styleable.TextAppearance;
    public static final int TextAppearance_android_fontFamily = com.kaolafm.opensdk.R.styleable.TextAppearance_android_fontFamily;
    public static final int TextAppearance_android_shadowColor = com.kaolafm.opensdk.R.styleable.TextAppearance_android_shadowColor;
    public static final int TextAppearance_android_shadowDx = com.kaolafm.opensdk.R.styleable.TextAppearance_android_shadowDx;
    public static final int TextAppearance_android_shadowDy = com.kaolafm.opensdk.R.styleable.TextAppearance_android_shadowDy;
    public static final int TextAppearance_android_shadowRadius = com.kaolafm.opensdk.R.styleable.TextAppearance_android_shadowRadius;
    public static final int TextAppearance_android_textColor = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textColor;
    public static final int TextAppearance_android_textColorHint = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textColorHint;
    public static final int TextAppearance_android_textColorLink = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textColorLink;
    public static final int TextAppearance_android_textFontWeight = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textFontWeight;
    public static final int TextAppearance_android_textSize = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textSize;
    public static final int TextAppearance_android_textStyle = com.kaolafm.opensdk.R.styleable.TextAppearance_android_textStyle;
    public static final int TextAppearance_android_typeface = com.kaolafm.opensdk.R.styleable.TextAppearance_android_typeface;
    public static final int TextAppearance_fontFamily = com.kaolafm.opensdk.R.styleable.TextAppearance_fontFamily;
    public static final int TextAppearance_fontVariationSettings = com.kaolafm.opensdk.R.styleable.TextAppearance_fontVariationSettings;
    public static final int TextAppearance_textAllCaps = com.kaolafm.opensdk.R.styleable.TextAppearance_textAllCaps;
    public static final int TextAppearance_textLocale = com.kaolafm.opensdk.R.styleable.TextAppearance_textLocale;
    public static final int[] Toolbar = com.kaolafm.opensdk.R.styleable.Toolbar;
    public static final int Toolbar_android_gravity = com.kaolafm.opensdk.R.styleable.Toolbar_android_gravity;
    public static final int Toolbar_android_minHeight = com.kaolafm.opensdk.R.styleable.Toolbar_android_minHeight;
    public static final int Toolbar_buttonGravity = com.kaolafm.opensdk.R.styleable.Toolbar_buttonGravity;
    public static final int Toolbar_collapseContentDescription = com.kaolafm.opensdk.R.styleable.Toolbar_collapseContentDescription;
    public static final int Toolbar_collapseIcon = com.kaolafm.opensdk.R.styleable.Toolbar_collapseIcon;
    public static final int Toolbar_contentInsetEnd = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetEnd;
    public static final int Toolbar_contentInsetEndWithActions = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetEndWithActions;
    public static final int Toolbar_contentInsetLeft = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetLeft;
    public static final int Toolbar_contentInsetRight = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetRight;
    public static final int Toolbar_contentInsetStart = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetStart;
    public static final int Toolbar_contentInsetStartWithNavigation = com.kaolafm.opensdk.R.styleable.Toolbar_contentInsetStartWithNavigation;
    public static final int Toolbar_logo = com.kaolafm.opensdk.R.styleable.Toolbar_logo;
    public static final int Toolbar_logoDescription = com.kaolafm.opensdk.R.styleable.Toolbar_logoDescription;
    public static final int Toolbar_maxButtonHeight = com.kaolafm.opensdk.R.styleable.Toolbar_maxButtonHeight;
    public static final int Toolbar_menu = com.kaolafm.opensdk.R.styleable.Toolbar_menu;
    public static final int Toolbar_navigationContentDescription = com.kaolafm.opensdk.R.styleable.Toolbar_navigationContentDescription;
    public static final int Toolbar_navigationIcon = com.kaolafm.opensdk.R.styleable.Toolbar_navigationIcon;
    public static final int Toolbar_popupTheme = com.kaolafm.opensdk.R.styleable.Toolbar_popupTheme;
    public static final int Toolbar_subtitle = com.kaolafm.opensdk.R.styleable.Toolbar_subtitle;
    public static final int Toolbar_subtitleTextAppearance = com.kaolafm.opensdk.R.styleable.Toolbar_subtitleTextAppearance;
    public static final int Toolbar_subtitleTextColor = com.kaolafm.opensdk.R.styleable.Toolbar_subtitleTextColor;
    public static final int Toolbar_title = com.kaolafm.opensdk.R.styleable.Toolbar_title;
    public static final int Toolbar_titleMargin = com.kaolafm.opensdk.R.styleable.Toolbar_titleMargin;
    public static final int Toolbar_titleMarginBottom = com.kaolafm.opensdk.R.styleable.Toolbar_titleMarginBottom;
    public static final int Toolbar_titleMarginEnd = com.kaolafm.opensdk.R.styleable.Toolbar_titleMarginEnd;
    public static final int Toolbar_titleMarginStart = com.kaolafm.opensdk.R.styleable.Toolbar_titleMarginStart;
    public static final int Toolbar_titleMarginTop = com.kaolafm.opensdk.R.styleable.Toolbar_titleMarginTop;
    public static final int Toolbar_titleMargins = com.kaolafm.opensdk.R.styleable.Toolbar_titleMargins;
    public static final int Toolbar_titleTextAppearance = com.kaolafm.opensdk.R.styleable.Toolbar_titleTextAppearance;
    public static final int Toolbar_titleTextColor = com.kaolafm.opensdk.R.styleable.Toolbar_titleTextColor;
    public static final int[] View = com.kaolafm.opensdk.R.styleable.View;
    public static final int View_android_focusable = com.kaolafm.opensdk.R.styleable.View_android_focusable;
    public static final int View_android_theme = com.kaolafm.opensdk.R.styleable.View_android_theme;
    public static final int View_paddingEnd = com.kaolafm.opensdk.R.styleable.View_paddingEnd;
    public static final int View_paddingStart = com.kaolafm.opensdk.R.styleable.View_paddingStart;
    public static final int View_theme = com.kaolafm.opensdk.R.styleable.View_theme;
    public static final int[] ViewBackgroundHelper = com.kaolafm.opensdk.R.styleable.ViewBackgroundHelper;
    public static final int ViewBackgroundHelper_android_background = com.kaolafm.opensdk.R.styleable.ViewBackgroundHelper_android_background;
    public static final int ViewBackgroundHelper_backgroundTint = com.kaolafm.opensdk.R.styleable.ViewBackgroundHelper_backgroundTint;
    public static final int ViewBackgroundHelper_backgroundTintMode = com.kaolafm.opensdk.R.styleable.ViewBackgroundHelper_backgroundTintMode;
    public static final int[] ViewStubCompat = com.kaolafm.opensdk.R.styleable.ViewStubCompat;
    public static final int ViewStubCompat_android_id = com.kaolafm.opensdk.R.styleable.ViewStubCompat_android_id;
    public static final int ViewStubCompat_android_inflatedId = com.kaolafm.opensdk.R.styleable.ViewStubCompat_android_inflatedId;
    public static final int ViewStubCompat_android_layout = com.kaolafm.opensdk.R.styleable.ViewStubCompat_android_layout;
    }
}
