<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\libs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\src\release\jniLibs"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni"><file name="arm64-v8a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni\arm64-v8a\libkaolafmse.so"/><file name="armeabi/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni\armeabi\libkaolafmse.so"/><file name="armeabi-v7a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni\armeabi-v7a\libkaolafmse.so"/><file name="x86/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni\x86\libkaolafmse.so"/><file name="x86_64/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\core\build\intermediates\exploded-aar\kaolaopensdk\utils\unspecified\release\jni\x86_64\libkaolafmse.so"/></source></dataSet></merger>