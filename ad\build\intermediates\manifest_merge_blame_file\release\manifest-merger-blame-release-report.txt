1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kaolafm.ad"
4    android:versionCode="10200"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml
10
11    <application>
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml:3:5-7:19
12        <service
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml:4:9-6:15
13            android:name="com.kaolafm.ad.timer.TimerJobService"
13-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml:4:18-55
14            android:permission="android.permission.BIND_JOB_SERVICE" />
14-->C:\Users\<USER>\AndroidStudioProjects\sdk\3.0-557\kaolaopensdk\ad\src\main\AndroidManifest.xml:5:13-69
15    </application>
16
17</manifest>
